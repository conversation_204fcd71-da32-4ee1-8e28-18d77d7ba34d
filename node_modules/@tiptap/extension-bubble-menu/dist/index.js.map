{"version": 3, "sources": ["../src/bubble-menu.ts", "../src/bubble-menu-plugin.ts", "../src/index.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\n\nimport type { BubbleMenuPluginProps } from './bubble-menu-plugin.js'\nimport { BubbleMenuPlugin } from './bubble-menu-plugin.js'\n\nexport type BubbleMenuOptions = Omit<BubbleMenuPluginProps, 'editor' | 'element'> & {\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement | null\n}\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nexport const BubbleMenu = Extension.create<BubbleMenuOptions>({\n  name: 'bubbleMenu',\n\n  addOptions() {\n    return {\n      element: null,\n      pluginKey: 'bubbleMenu',\n      updateDelay: undefined,\n      appendTo: undefined,\n      shouldShow: null,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    if (!this.options.element) {\n      return []\n    }\n\n    return [\n      BubbleMenuPlugin({\n        pluginKey: this.options.plugin<PERSON><PERSON>,\n        editor: this.editor,\n        element: this.options.element,\n        updateDelay: this.options.updateDelay,\n        options: this.options.options,\n        appendTo: this.options.appendTo,\n        shouldShow: this.options.shouldShow,\n      }),\n    ]\n  },\n})\n", "import {\n  type Middleware,\n  arrow,\n  autoPlacement,\n  computePosition,\n  flip,\n  hide,\n  inline,\n  offset,\n  shift,\n  size,\n} from '@floating-ui/dom'\nimport type { Editor } from '@tiptap/core'\nimport { isTextSelection, posToDOMRect } from '@tiptap/core'\nimport type { EditorState, PluginView } from '@tiptap/pm/state'\nimport { NodeSelection, Plugin, PluginKey } from '@tiptap/pm/state'\nimport { CellSelection } from '@tiptap/pm/tables'\nimport type { EditorView } from '@tiptap/pm/view'\n\nfunction combineDOMRects(rect1: DOMRect, rect2: DOMRect): DOMRect {\n  const top = Math.min(rect1.top, rect2.top)\n  const bottom = Math.max(rect1.bottom, rect2.bottom)\n  const left = Math.min(rect1.left, rect2.left)\n  const right = Math.max(rect1.right, rect2.right)\n  const width = right - left\n  const height = bottom - top\n  const x = left\n  const y = top\n  return new DOMRect(x, y, width, height)\n}\n\nexport interface BubbleMenuPluginProps {\n  /**\n   * The plugin key.\n   * @type {PluginKey | string}\n   * @default 'bubbleMenu'\n   */\n  pluginKey: PluginKey | string\n\n  /**\n   * The editor instance.\n   */\n  editor: Editor\n\n  /**\n   * The DOM element that contains your menu.\n   * @type {HTMLElement}\n   * @default null\n   */\n  element: HTMLElement\n\n  /**\n   * The delay in milliseconds before the menu should be updated.\n   * This can be useful to prevent performance issues.\n   * @type {number}\n   * @default 250\n   */\n  updateDelay?: number\n\n  /**\n   * The delay in milliseconds before the menu position should be updated on window resize.\n   * This can be useful to prevent performance issues.\n   * @type {number}\n   * @default 60\n   */\n  resizeDelay?: number\n\n  /**\n   * A function that determines whether the menu should be shown or not.\n   * If this function returns `false`, the menu will be hidden, otherwise it will be shown.\n   */\n  shouldShow?:\n    | ((props: {\n        editor: Editor\n        element: HTMLElement\n        view: EditorView\n        state: EditorState\n        oldState?: EditorState\n        from: number\n        to: number\n      }) => boolean)\n    | null\n\n  /**\n   * The DOM element to append your menu to. Default is the editor's parent element.\n   *\n   * Sometimes the menu needs to be appended to a different DOM context due to accessibility, clipping, or z-index issues.\n   *\n   * @type {HTMLElement}\n   * @default null\n   */\n  appendTo?: HTMLElement\n\n  /**\n   * The options for the bubble menu. Those are passed to Floating UI and include options for the placement, offset, flip, shift, arrow, size, autoPlacement,\n   * hide, and inline middlewares.\n   * @default {}\n   * @see https://floating-ui.com/docs/computePosition#options\n   */\n  options?: {\n    strategy?: 'absolute' | 'fixed'\n    placement?:\n      | 'top'\n      | 'right'\n      | 'bottom'\n      | 'left'\n      | 'top-start'\n      | 'top-end'\n      | 'right-start'\n      | 'right-end'\n      | 'bottom-start'\n      | 'bottom-end'\n      | 'left-start'\n      | 'left-end'\n    offset?: Parameters<typeof offset>[0] | boolean\n    flip?: Parameters<typeof flip>[0] | boolean\n    shift?: Parameters<typeof shift>[0] | boolean\n    arrow?: Parameters<typeof arrow>[0] | false\n    size?: Parameters<typeof size>[0] | boolean\n    autoPlacement?: Parameters<typeof autoPlacement>[0] | boolean\n    hide?: Parameters<typeof hide>[0] | boolean\n    inline?: Parameters<typeof inline>[0] | boolean\n\n    onShow?: () => void\n    onHide?: () => void\n    onUpdate?: () => void\n    onDestroy?: () => void\n  }\n}\n\nexport type BubbleMenuViewProps = BubbleMenuPluginProps & {\n  view: EditorView\n}\n\nexport class BubbleMenuView implements PluginView {\n  public editor: Editor\n\n  public element: HTMLElement\n\n  public view: EditorView\n\n  public preventHide = false\n\n  public updateDelay: number\n\n  public resizeDelay: number\n\n  public appendTo: HTMLElement | undefined\n\n  private updateDebounceTimer: number | undefined\n\n  private resizeDebounceTimer: number | undefined\n\n  private isVisible = false\n\n  private floatingUIOptions: NonNullable<BubbleMenuPluginProps['options']> = {\n    strategy: 'absolute',\n    placement: 'top',\n    offset: 8,\n    flip: {},\n    shift: {},\n    arrow: false,\n    size: false,\n    autoPlacement: false,\n    hide: false,\n    inline: false,\n    onShow: undefined,\n    onHide: undefined,\n    onUpdate: undefined,\n    onDestroy: undefined,\n  }\n\n  public shouldShow: Exclude<BubbleMenuPluginProps['shouldShow'], null> = ({ view, state, from, to }) => {\n    const { doc, selection } = state\n    const { empty } = selection\n\n    // Sometime check for `empty` is not enough.\n    // Doubleclick an empty paragraph returns a node size of 2.\n    // So we check also for an empty text size.\n    const isEmptyTextBlock = !doc.textBetween(from, to).length && isTextSelection(state.selection)\n\n    // When clicking on a element inside the bubble menu the editor \"blur\" event\n    // is called and the bubble menu item is focussed. In this case we should\n    // consider the menu as part of the editor and keep showing the menu\n    const isChildOfMenu = this.element.contains(document.activeElement)\n\n    const hasEditorFocus = view.hasFocus() || isChildOfMenu\n\n    if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n      return false\n    }\n\n    return true\n  }\n\n  get middlewares() {\n    const middlewares: Middleware[] = []\n\n    if (this.floatingUIOptions.flip) {\n      middlewares.push(flip(typeof this.floatingUIOptions.flip !== 'boolean' ? this.floatingUIOptions.flip : undefined))\n    }\n\n    if (this.floatingUIOptions.shift) {\n      middlewares.push(\n        shift(typeof this.floatingUIOptions.shift !== 'boolean' ? this.floatingUIOptions.shift : undefined),\n      )\n    }\n\n    if (this.floatingUIOptions.offset) {\n      middlewares.push(\n        offset(typeof this.floatingUIOptions.offset !== 'boolean' ? this.floatingUIOptions.offset : undefined),\n      )\n    }\n\n    if (this.floatingUIOptions.arrow) {\n      middlewares.push(arrow(this.floatingUIOptions.arrow))\n    }\n\n    if (this.floatingUIOptions.size) {\n      middlewares.push(size(typeof this.floatingUIOptions.size !== 'boolean' ? this.floatingUIOptions.size : undefined))\n    }\n\n    if (this.floatingUIOptions.autoPlacement) {\n      middlewares.push(\n        autoPlacement(\n          typeof this.floatingUIOptions.autoPlacement !== 'boolean' ? this.floatingUIOptions.autoPlacement : undefined,\n        ),\n      )\n    }\n\n    if (this.floatingUIOptions.hide) {\n      middlewares.push(hide(typeof this.floatingUIOptions.hide !== 'boolean' ? this.floatingUIOptions.hide : undefined))\n    }\n\n    if (this.floatingUIOptions.inline) {\n      middlewares.push(\n        inline(typeof this.floatingUIOptions.inline !== 'boolean' ? this.floatingUIOptions.inline : undefined),\n      )\n    }\n\n    return middlewares\n  }\n\n  constructor({\n    editor,\n    element,\n    view,\n    updateDelay = 250,\n    resizeDelay = 60,\n    shouldShow,\n    appendTo,\n    options,\n  }: BubbleMenuViewProps) {\n    this.editor = editor\n    this.element = element\n    this.view = view\n    this.updateDelay = updateDelay\n    this.resizeDelay = resizeDelay\n    this.appendTo = appendTo\n\n    this.floatingUIOptions = {\n      ...this.floatingUIOptions,\n      ...options,\n    }\n\n    this.element.tabIndex = 0\n\n    if (shouldShow) {\n      this.shouldShow = shouldShow\n    }\n\n    this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.addEventListener('dragstart', this.dragstartHandler)\n    this.editor.on('focus', this.focusHandler)\n    this.editor.on('blur', this.blurHandler)\n    window.addEventListener('resize', this.resizeHandler)\n\n    this.update(view, view.state)\n\n    if (this.getShouldShow()) {\n      this.show()\n    }\n  }\n\n  mousedownHandler = () => {\n    this.preventHide = true\n  }\n\n  dragstartHandler = () => {\n    this.hide()\n  }\n\n  /**\n   * Handles the window resize event to update the position of the bubble menu.\n   * It uses a debounce mechanism to prevent excessive updates.\n   * The delay is defined by the `resizeDelay` property.\n   */\n  resizeHandler = () => {\n    if (this.resizeDebounceTimer) {\n      clearTimeout(this.resizeDebounceTimer)\n    }\n\n    this.resizeDebounceTimer = window.setTimeout(() => {\n      this.updatePosition()\n    }, this.resizeDelay)\n  }\n\n  focusHandler = () => {\n    // we use `setTimeout` to make sure `selection` is already updated\n    setTimeout(() => this.update(this.editor.view))\n  }\n\n  blurHandler = ({ event }: { event: FocusEvent }) => {\n    if (this.preventHide) {\n      this.preventHide = false\n\n      return\n    }\n\n    if (event?.relatedTarget && this.element.parentNode?.contains(event.relatedTarget as Node)) {\n      return\n    }\n\n    if (event?.relatedTarget === this.editor.view.dom) {\n      return\n    }\n\n    this.hide()\n  }\n\n  updatePosition() {\n    const { selection } = this.editor.state\n    const domRect = posToDOMRect(this.view, selection.from, selection.to)\n    let virtualElement = {\n      getBoundingClientRect: () => domRect,\n      getClientRects: () => [domRect],\n    }\n\n    if (selection instanceof NodeSelection) {\n      let node = this.view.nodeDOM(selection.from) as HTMLElement\n\n      const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]')\n\n      if (nodeViewWrapper) {\n        node = nodeViewWrapper as HTMLElement\n      }\n\n      if (node) {\n        virtualElement = {\n          getBoundingClientRect: () => node.getBoundingClientRect(),\n          getClientRects: () => [node.getBoundingClientRect()],\n        }\n      }\n    }\n\n    // this is a special case for cell selections\n    if (selection instanceof CellSelection) {\n      const { $anchorCell, $headCell } = selection\n\n      const from = $anchorCell ? $anchorCell.pos : $headCell!.pos\n      const to = $headCell ? $headCell.pos : $anchorCell!.pos\n\n      const fromDOM = this.view.nodeDOM(from)\n      const toDOM = this.view.nodeDOM(to)\n\n      if (!fromDOM || !toDOM) {\n        return\n      }\n\n      const clientRect =\n        fromDOM === toDOM\n          ? (fromDOM as HTMLElement).getBoundingClientRect()\n          : combineDOMRects(\n              (fromDOM as HTMLElement).getBoundingClientRect(),\n              (toDOM as HTMLElement).getBoundingClientRect(),\n            )\n\n      virtualElement = {\n        getBoundingClientRect: () => clientRect,\n        getClientRects: () => [clientRect],\n      }\n    }\n\n    computePosition(virtualElement, this.element, {\n      placement: this.floatingUIOptions.placement,\n      strategy: this.floatingUIOptions.strategy,\n      middleware: this.middlewares,\n    }).then(({ x, y, strategy }) => {\n      this.element.style.width = 'max-content'\n      this.element.style.position = strategy\n      this.element.style.left = `${x}px`\n      this.element.style.top = `${y}px`\n\n      if (this.isVisible && this.floatingUIOptions.onUpdate) {\n        this.floatingUIOptions.onUpdate()\n      }\n    })\n  }\n\n  update(view: EditorView, oldState?: EditorState) {\n    const { state } = view\n    const hasValidSelection = state.selection.from !== state.selection.to\n\n    if (this.updateDelay > 0 && hasValidSelection) {\n      this.handleDebouncedUpdate(view, oldState)\n      return\n    }\n\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    this.updateHandler(view, selectionChanged, docChanged, oldState)\n  }\n\n  handleDebouncedUpdate = (view: EditorView, oldState?: EditorState) => {\n    const selectionChanged = !oldState?.selection.eq(view.state.selection)\n    const docChanged = !oldState?.doc.eq(view.state.doc)\n\n    if (!selectionChanged && !docChanged) {\n      return\n    }\n\n    if (this.updateDebounceTimer) {\n      clearTimeout(this.updateDebounceTimer)\n    }\n\n    this.updateDebounceTimer = window.setTimeout(() => {\n      this.updateHandler(view, selectionChanged, docChanged, oldState)\n    }, this.updateDelay)\n  }\n\n  getShouldShow(oldState?: EditorState) {\n    const { state } = this.view\n    const { selection } = state\n\n    // support for CellSelections\n    const { ranges } = selection\n    const from = Math.min(...ranges.map(range => range.$from.pos))\n    const to = Math.max(...ranges.map(range => range.$to.pos))\n\n    const shouldShow = this.shouldShow?.({\n      editor: this.editor,\n      element: this.element,\n      view: this.view,\n      state,\n      oldState,\n      from,\n      to,\n    })\n\n    return shouldShow\n  }\n\n  updateHandler = (view: EditorView, selectionChanged: boolean, docChanged: boolean, oldState?: EditorState) => {\n    const { composing } = view\n\n    const isSame = !selectionChanged && !docChanged\n\n    if (composing || isSame) {\n      return\n    }\n\n    const shouldShow = this.getShouldShow(oldState)\n\n    if (!shouldShow) {\n      this.hide()\n\n      return\n    }\n\n    this.updatePosition()\n    this.show()\n  }\n\n  show() {\n    if (this.isVisible) {\n      return\n    }\n\n    this.element.style.visibility = 'visible'\n    this.element.style.opacity = '1'\n    // attach to appendTo or editor's parent element\n    ;(this.appendTo ?? this.view.dom.parentElement)?.appendChild(this.element)\n\n    if (this.floatingUIOptions.onShow) {\n      this.floatingUIOptions.onShow()\n    }\n\n    this.isVisible = true\n  }\n\n  hide() {\n    if (!this.isVisible) {\n      return\n    }\n\n    this.element.style.visibility = 'hidden'\n    this.element.style.opacity = '0'\n    // remove from the parent element\n    this.element.remove()\n\n    if (this.floatingUIOptions.onHide) {\n      this.floatingUIOptions.onHide()\n    }\n\n    this.isVisible = false\n  }\n\n  destroy() {\n    this.hide()\n    this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true })\n    this.view.dom.removeEventListener('dragstart', this.dragstartHandler)\n    window.removeEventListener('resize', this.resizeHandler)\n    this.editor.off('focus', this.focusHandler)\n    this.editor.off('blur', this.blurHandler)\n\n    if (this.floatingUIOptions.onDestroy) {\n      this.floatingUIOptions.onDestroy()\n    }\n  }\n}\n\nexport const BubbleMenuPlugin = (options: BubbleMenuPluginProps) => {\n  return new Plugin({\n    key: typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,\n    view: view => new BubbleMenuView({ view, ...options }),\n  })\n}\n", "import { BubbleMenu } from './bubble-menu.js'\n\nexport * from './bubble-menu.js'\nexport * from './bubble-menu-plugin.js'\n\nexport default BubbleMenu\n"], "mappings": ";AAAA,SAAS,iBAAiB;;;ACA1B;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,SAAS,iBAAiB,oBAAoB;AAE9C,SAAS,eAAe,QAAQ,iBAAiB;AACjD,SAAS,qBAAqB;AAG9B,SAAS,gBAAgB,OAAgB,OAAyB;AAChE,QAAM,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,GAAG;AACzC,QAAM,SAAS,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AAClD,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI;AAC5C,QAAM,QAAQ,KAAK,IAAI,MAAM,OAAO,MAAM,KAAK;AAC/C,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,SAAS;AACxB,QAAM,IAAI;AACV,QAAM,IAAI;AACV,SAAO,IAAI,QAAQ,GAAG,GAAG,OAAO,MAAM;AACxC;AAyGO,IAAM,iBAAN,MAA2C;AAAA,EA6GhD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAwB;AA/GxB,SAAO,cAAc;AAYrB,SAAQ,YAAY;AAEpB,SAAQ,oBAAmE;AAAA,MACzE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,MACP,OAAO,CAAC;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAEA,SAAO,aAAiE,CAAC,EAAE,MAAM,OAAO,MAAM,GAAG,MAAM;AACrG,YAAM,EAAE,KAAK,UAAU,IAAI;AAC3B,YAAM,EAAE,MAAM,IAAI;AAKlB,YAAM,mBAAmB,CAAC,IAAI,YAAY,MAAM,EAAE,EAAE,UAAU,gBAAgB,MAAM,SAAS;AAK7F,YAAM,gBAAgB,KAAK,QAAQ,SAAS,SAAS,aAAa;AAElE,YAAM,iBAAiB,KAAK,SAAS,KAAK;AAE1C,UAAI,CAAC,kBAAkB,SAAS,oBAAoB,CAAC,KAAK,OAAO,YAAY;AAC3E,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AA2FA,4BAAmB,MAAM;AACvB,WAAK,cAAc;AAAA,IACrB;AAEA,4BAAmB,MAAM;AACvB,WAAK,KAAK;AAAA,IACZ;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAgB,MAAM;AACpB,UAAI,KAAK,qBAAqB;AAC5B,qBAAa,KAAK,mBAAmB;AAAA,MACvC;AAEA,WAAK,sBAAsB,OAAO,WAAW,MAAM;AACjD,aAAK,eAAe;AAAA,MACtB,GAAG,KAAK,WAAW;AAAA,IACrB;AAEA,wBAAe,MAAM;AAEnB,iBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,CAAC;AAAA,IAChD;AAEA,uBAAc,CAAC,EAAE,MAAM,MAA6B;AAxTtD;AAyTI,UAAI,KAAK,aAAa;AACpB,aAAK,cAAc;AAEnB;AAAA,MACF;AAEA,WAAI,+BAAO,oBAAiB,UAAK,QAAQ,eAAb,mBAAyB,SAAS,MAAM,iBAAwB;AAC1F;AAAA,MACF;AAEA,WAAI,+BAAO,mBAAkB,KAAK,OAAO,KAAK,KAAK;AACjD;AAAA,MACF;AAEA,WAAK,KAAK;AAAA,IACZ;AAsFA,iCAAwB,CAAC,MAAkB,aAA2B;AACpE,YAAM,mBAAmB,EAAC,qCAAU,UAAU,GAAG,KAAK,MAAM;AAC5D,YAAM,aAAa,EAAC,qCAAU,IAAI,GAAG,KAAK,MAAM;AAEhD,UAAI,CAAC,oBAAoB,CAAC,YAAY;AACpC;AAAA,MACF;AAEA,UAAI,KAAK,qBAAqB;AAC5B,qBAAa,KAAK,mBAAmB;AAAA,MACvC;AAEA,WAAK,sBAAsB,OAAO,WAAW,MAAM;AACjD,aAAK,cAAc,MAAM,kBAAkB,YAAY,QAAQ;AAAA,MACjE,GAAG,KAAK,WAAW;AAAA,IACrB;AAwBA,yBAAgB,CAAC,MAAkB,kBAA2B,YAAqB,aAA2B;AAC5G,YAAM,EAAE,UAAU,IAAI;AAEtB,YAAM,SAAS,CAAC,oBAAoB,CAAC;AAErC,UAAI,aAAa,QAAQ;AACvB;AAAA,MACF;AAEA,YAAM,aAAa,KAAK,cAAc,QAAQ;AAE9C,UAAI,CAAC,YAAY;AACf,aAAK,KAAK;AAEV;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,KAAK;AAAA,IACZ;AA3NE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,WAAW;AAEhB,SAAK,oBAAoB;AAAA,MACvB,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACL;AAEA,SAAK,QAAQ,WAAW;AAExB,QAAI,YAAY;AACd,WAAK,aAAa;AAAA,IACpB;AAEA,SAAK,QAAQ,iBAAiB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAK,CAAC;AACnF,SAAK,KAAK,IAAI,iBAAiB,aAAa,KAAK,gBAAgB;AACjE,SAAK,OAAO,GAAG,SAAS,KAAK,YAAY;AACzC,SAAK,OAAO,GAAG,QAAQ,KAAK,WAAW;AACvC,WAAO,iBAAiB,UAAU,KAAK,aAAa;AAEpD,SAAK,OAAO,MAAM,KAAK,KAAK;AAE5B,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EAvFA,IAAI,cAAc;AAChB,UAAM,cAA4B,CAAC;AAEnC,QAAI,KAAK,kBAAkB,MAAM;AAC/B,kBAAY,KAAK,KAAK,OAAO,KAAK,kBAAkB,SAAS,YAAY,KAAK,kBAAkB,OAAO,MAAS,CAAC;AAAA,IACnH;AAEA,QAAI,KAAK,kBAAkB,OAAO;AAChC,kBAAY;AAAA,QACV,MAAM,OAAO,KAAK,kBAAkB,UAAU,YAAY,KAAK,kBAAkB,QAAQ,MAAS;AAAA,MACpG;AAAA,IACF;AAEA,QAAI,KAAK,kBAAkB,QAAQ;AACjC,kBAAY;AAAA,QACV,OAAO,OAAO,KAAK,kBAAkB,WAAW,YAAY,KAAK,kBAAkB,SAAS,MAAS;AAAA,MACvG;AAAA,IACF;AAEA,QAAI,KAAK,kBAAkB,OAAO;AAChC,kBAAY,KAAK,MAAM,KAAK,kBAAkB,KAAK,CAAC;AAAA,IACtD;AAEA,QAAI,KAAK,kBAAkB,MAAM;AAC/B,kBAAY,KAAK,KAAK,OAAO,KAAK,kBAAkB,SAAS,YAAY,KAAK,kBAAkB,OAAO,MAAS,CAAC;AAAA,IACnH;AAEA,QAAI,KAAK,kBAAkB,eAAe;AACxC,kBAAY;AAAA,QACV;AAAA,UACE,OAAO,KAAK,kBAAkB,kBAAkB,YAAY,KAAK,kBAAkB,gBAAgB;AAAA,QACrG;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,kBAAkB,MAAM;AAC/B,kBAAY,KAAK,KAAK,OAAO,KAAK,kBAAkB,SAAS,YAAY,KAAK,kBAAkB,OAAO,MAAS,CAAC;AAAA,IACnH;AAEA,QAAI,KAAK,kBAAkB,QAAQ;AACjC,kBAAY;AAAA,QACV,OAAO,OAAO,KAAK,kBAAkB,WAAW,YAAY,KAAK,kBAAkB,SAAS,MAAS;AAAA,MACvG;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAyFA,iBAAiB;AACf,UAAM,EAAE,UAAU,IAAI,KAAK,OAAO;AAClC,UAAM,UAAU,aAAa,KAAK,MAAM,UAAU,MAAM,UAAU,EAAE;AACpE,QAAI,iBAAiB;AAAA,MACnB,uBAAuB,MAAM;AAAA,MAC7B,gBAAgB,MAAM,CAAC,OAAO;AAAA,IAChC;AAEA,QAAI,qBAAqB,eAAe;AACtC,UAAI,OAAO,KAAK,KAAK,QAAQ,UAAU,IAAI;AAE3C,YAAM,kBAAkB,KAAK,QAAQ,kBAAkB,OAAO,KAAK,cAAc,0BAA0B;AAE3G,UAAI,iBAAiB;AACnB,eAAO;AAAA,MACT;AAEA,UAAI,MAAM;AACR,yBAAiB;AAAA,UACf,uBAAuB,MAAM,KAAK,sBAAsB;AAAA,UACxD,gBAAgB,MAAM,CAAC,KAAK,sBAAsB,CAAC;AAAA,QACrD;AAAA,MACF;AAAA,IACF;AAGA,QAAI,qBAAqB,eAAe;AACtC,YAAM,EAAE,aAAa,UAAU,IAAI;AAEnC,YAAM,OAAO,cAAc,YAAY,MAAM,UAAW;AACxD,YAAM,KAAK,YAAY,UAAU,MAAM,YAAa;AAEpD,YAAM,UAAU,KAAK,KAAK,QAAQ,IAAI;AACtC,YAAM,QAAQ,KAAK,KAAK,QAAQ,EAAE;AAElC,UAAI,CAAC,WAAW,CAAC,OAAO;AACtB;AAAA,MACF;AAEA,YAAM,aACJ,YAAY,QACP,QAAwB,sBAAsB,IAC/C;AAAA,QACG,QAAwB,sBAAsB;AAAA,QAC9C,MAAsB,sBAAsB;AAAA,MAC/C;AAEN,uBAAiB;AAAA,QACf,uBAAuB,MAAM;AAAA,QAC7B,gBAAgB,MAAM,CAAC,UAAU;AAAA,MACnC;AAAA,IACF;AAEA,oBAAgB,gBAAgB,KAAK,SAAS;AAAA,MAC5C,WAAW,KAAK,kBAAkB;AAAA,MAClC,UAAU,KAAK,kBAAkB;AAAA,MACjC,YAAY,KAAK;AAAA,IACnB,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,GAAG,SAAS,MAAM;AAC9B,WAAK,QAAQ,MAAM,QAAQ;AAC3B,WAAK,QAAQ,MAAM,WAAW;AAC9B,WAAK,QAAQ,MAAM,OAAO,GAAG,CAAC;AAC9B,WAAK,QAAQ,MAAM,MAAM,GAAG,CAAC;AAE7B,UAAI,KAAK,aAAa,KAAK,kBAAkB,UAAU;AACrD,aAAK,kBAAkB,SAAS;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,OAAO,MAAkB,UAAwB;AAC/C,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,oBAAoB,MAAM,UAAU,SAAS,MAAM,UAAU;AAEnE,QAAI,KAAK,cAAc,KAAK,mBAAmB;AAC7C,WAAK,sBAAsB,MAAM,QAAQ;AACzC;AAAA,IACF;AAEA,UAAM,mBAAmB,EAAC,qCAAU,UAAU,GAAG,KAAK,MAAM;AAC5D,UAAM,aAAa,EAAC,qCAAU,IAAI,GAAG,KAAK,MAAM;AAEhD,SAAK,cAAc,MAAM,kBAAkB,YAAY,QAAQ;AAAA,EACjE;AAAA,EAmBA,cAAc,UAAwB;AA/axC;AAgbI,UAAM,EAAE,MAAM,IAAI,KAAK;AACvB,UAAM,EAAE,UAAU,IAAI;AAGtB,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC;AAC7D,UAAM,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,MAAM,IAAI,GAAG,CAAC;AAEzD,UAAM,cAAa,UAAK,eAAL,8BAAkB;AAAA,MACnC,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,MAAM,KAAK;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAuBA,OAAO;AA1dT;AA2dI,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AAEA,SAAK,QAAQ,MAAM,aAAa;AAChC,SAAK,QAAQ,MAAM,UAAU;AAE5B,KAAC,gBAAK,aAAL,YAAiB,KAAK,KAAK,IAAI,kBAA/B,mBAA+C,YAAY,KAAK;AAElE,QAAI,KAAK,kBAAkB,QAAQ;AACjC,WAAK,kBAAkB,OAAO;AAAA,IAChC;AAEA,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AAEA,SAAK,QAAQ,MAAM,aAAa;AAChC,SAAK,QAAQ,MAAM,UAAU;AAE7B,SAAK,QAAQ,OAAO;AAEpB,QAAI,KAAK,kBAAkB,QAAQ;AACjC,WAAK,kBAAkB,OAAO;AAAA,IAChC;AAEA,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,UAAU;AACR,SAAK,KAAK;AACV,SAAK,QAAQ,oBAAoB,aAAa,KAAK,kBAAkB,EAAE,SAAS,KAAK,CAAC;AACtF,SAAK,KAAK,IAAI,oBAAoB,aAAa,KAAK,gBAAgB;AACpE,WAAO,oBAAoB,UAAU,KAAK,aAAa;AACvD,SAAK,OAAO,IAAI,SAAS,KAAK,YAAY;AAC1C,SAAK,OAAO,IAAI,QAAQ,KAAK,WAAW;AAExC,QAAI,KAAK,kBAAkB,WAAW;AACpC,WAAK,kBAAkB,UAAU;AAAA,IACnC;AAAA,EACF;AACF;AAEO,IAAM,mBAAmB,CAAC,YAAmC;AAClE,SAAO,IAAI,OAAO;AAAA,IAChB,KAAK,OAAO,QAAQ,cAAc,WAAW,IAAI,UAAU,QAAQ,SAAS,IAAI,QAAQ;AAAA,IACxF,MAAM,UAAQ,IAAI,eAAe,EAAE,MAAM,GAAG,QAAQ,CAAC;AAAA,EACvD,CAAC;AACH;;;AD7fO,IAAM,aAAa,UAAU,OAA0B;AAAA,EAC5D,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAO,CAAC;AAAA,IACV;AAEA,WAAO;AAAA,MACL,iBAAiB;AAAA,QACf,WAAW,KAAK,QAAQ;AAAA,QACxB,QAAQ,KAAK;AAAA,QACb,SAAS,KAAK,QAAQ;AAAA,QACtB,aAAa,KAAK,QAAQ;AAAA,QAC1B,SAAS,KAAK,QAAQ;AAAA,QACtB,UAAU,KAAK,QAAQ;AAAA,QACvB,YAAY,KAAK,QAAQ;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AE3CD,IAAO,gBAAQ;", "names": []}