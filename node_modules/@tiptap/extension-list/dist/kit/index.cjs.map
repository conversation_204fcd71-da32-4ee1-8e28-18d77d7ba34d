{"version": 3, "sources": ["../../src/kit/index.ts", "../../src/bullet-list/bullet-list.ts", "../../src/item/list-item.ts", "../../src/keymap/list-keymap.ts", "../../src/keymap/listHelpers/index.ts", "../../src/keymap/listHelpers/findListItemPos.ts", "../../src/keymap/listHelpers/getNextListDepth.ts", "../../src/keymap/listHelpers/handleBackspace.ts", "../../src/keymap/listHelpers/hasListBefore.ts", "../../src/keymap/listHelpers/hasListItemBefore.ts", "../../src/keymap/listHelpers/listItemHasSubList.ts", "../../src/keymap/listHelpers/handleDelete.ts", "../../src/keymap/listHelpers/nextListIsDeeper.ts", "../../src/keymap/listHelpers/nextListIsHigher.ts", "../../src/keymap/listHelpers/hasListItemAfter.ts", "../../src/ordered-list/ordered-list.ts", "../../src/task-item/task-item.ts", "../../src/task-list/task-list.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\n\nimport type { BulletListOptions } from '../bullet-list/index.js'\nimport { BulletList } from '../bullet-list/index.js'\nimport type { ListItemOptions } from '../item/index.js'\nimport { ListItem } from '../item/index.js'\nimport type { ListKeymapOptions } from '../keymap/index.js'\nimport { ListKeymap } from '../keymap/index.js'\nimport type { OrderedListOptions } from '../ordered-list/index.js'\nimport { OrderedList } from '../ordered-list/index.js'\nimport type { TaskItemOptions } from '../task-item/index.js'\nimport { TaskItem } from '../task-item/index.js'\nimport type { TaskListOptions } from '../task-list/index.js'\nimport { TaskList } from '../task-list/index.js'\n\nexport interface ListKitOptions {\n  /**\n   * If set to false, the bulletList extension will not be registered\n   * @example table: false\n   */\n  bulletList: Partial<BulletListOptions> | false\n  /**\n   * If set to false, the listItem extension will not be registered\n   */\n  listItem: Partial<ListItemOptions> | false\n  /**\n   * If set to false, the listKeymap extension will not be registered\n   */\n  listKeymap: Partial<ListKeymapOptions> | false\n  /**\n   * If set to false, the orderedList extension will not be registered\n   */\n  orderedList: Partial<OrderedListOptions> | false\n  /**\n   * If set to false, the taskItem extension will not be registered\n   */\n  taskItem: Partial<TaskItemOptions> | false\n  /**\n   * If set to false, the taskList extension will not be registered\n   */\n  taskList: Partial<TaskListOptions> | false\n}\n\n/**\n * The table kit is a collection of table editor extensions.\n *\n * It’s a good starting point for building your own table in Tiptap.\n */\nexport const ListKit = Extension.create<ListKitOptions>({\n  name: 'listKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.bulletList !== false) {\n      extensions.push(BulletList.configure(this.options.bulletList))\n    }\n\n    if (this.options.listItem !== false) {\n      extensions.push(ListItem.configure(this.options.listItem))\n    }\n\n    if (this.options.listKeymap !== false) {\n      extensions.push(ListKeymap.configure(this.options.listKeymap))\n    }\n\n    if (this.options.orderedList !== false) {\n      extensions.push(OrderedList.configure(this.options.orderedList))\n    }\n\n    if (this.options.taskItem !== false) {\n      extensions.push(TaskItem.configure(this.options.taskItem))\n    }\n\n    if (this.options.taskList !== false) {\n      extensions.push(TaskList.configure(this.options.taskList))\n    }\n\n    return extensions\n  },\n})\n", "import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface BulletListOptions {\n  /**\n   * The node name for the list items\n   * @default 'listItem'\n   * @example 'paragraph'\n   */\n  itemTypeName: string\n\n  /**\n   * HTML attributes to add to the bullet list element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * Keep the marks when splitting the list\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean\n\n  /**\n   * Keep the attributes when splitting the list\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bulletList: {\n      /**\n       * Toggle a bullet list\n       */\n      toggleBulletList: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nexport const bulletListInputRegex = /^\\s*([-+*])\\s$/\n\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nexport const BulletList = Node.create<BulletListOptions>({\n  name: 'bulletList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [{ tag: 'ul' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleBulletList:\n        () =>\n        ({ commands, chain }) => {\n          if (this.options.keepAttributes) {\n            return chain()\n              .toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n              .updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName))\n              .run()\n          }\n          return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: bulletListInputRegex,\n      type: this.type,\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: bulletListInputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: () => {\n          return this.editor.getAttributes(TextStyleName)\n        },\n        editor: this.editor,\n      })\n    }\n    return [inputRule]\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ListItemOptions {\n  /**\n   * The HTML attributes for a list item node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * The node type for bulletList nodes\n   * @default 'bulletList'\n   * @example 'myCustomBulletList'\n   */\n  bulletListTypeName: string\n\n  /**\n   * The node type for orderedList nodes\n   * @default 'orderedList'\n   * @example 'myCustomOrderedList'\n   */\n  orderedListTypeName: string\n}\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const ListItem = Node.create<ListItemOptions>({\n  name: 'listItem',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      bulletListTypeName: 'bulletList',\n      orderedListTypeName: 'orderedList',\n    }\n  },\n\n  content: 'paragraph block*',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      {\n        tag: 'li',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n  },\n})\n", "import { Extension } from '@tiptap/core'\n\nimport { handleBackspace, handleDelete } from './listHelpers/index.js'\n\nexport type ListKeymapOptions = {\n  /**\n   * An array of list types. This is used for item and wrapper list matching.\n   * @default []\n   * @example [{ itemName: 'listItem', wrapperNames: ['bulletList', 'orderedList'] }]\n   */\n  listTypes: Array<{\n    itemName: string\n    wrapperNames: string[]\n  }>\n}\n\n/**\n * This extension registers custom keymaps to change the behaviour of the backspace and delete keys.\n * By default Prosemirror keyhandling will always lift or sink items so paragraphs are joined into\n * the adjacent or previous list item. This extension will prevent this behaviour and instead will\n * try to join paragraphs from two list items into a single list item.\n * @see https://www.tiptap.dev/api/extensions/list-keymap\n */\nexport const ListKeymap = Extension.create<ListKeymapOptions>({\n  name: 'listKeymap',\n\n  addOptions() {\n    return {\n      listTypes: [\n        {\n          itemName: 'listItem',\n          wrapperNames: ['bulletList', 'orderedList'],\n        },\n        {\n          itemName: 'taskItem',\n          wrapperNames: ['taskList'],\n        },\n      ],\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Delete: ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleDelete(editor, itemName)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      'Mod-Delete': ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleDelete(editor, itemName)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      Backspace: ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName, wrapperNames }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleBackspace(editor, itemName, wrapperNames)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      'Mod-Backspace': ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName, wrapperNames }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleBackspace(editor, itemName, wrapperNames)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n    }\n  },\n})\n", "export * from './findListItemPos.js'\nexport * from './getNextListDepth.js'\nexport * from './handleBackspace.js'\nexport * from './handleDelete.js'\nexport * from './hasListBefore.js'\nexport * from './hasListItemAfter.js'\nexport * from './hasListItemBefore.js'\nexport * from './listItemHasSubList.js'\nexport * from './nextListIsDeeper.js'\nexport * from './nextListIsHigher.js'\n", "import { getNodeType } from '@tiptap/core'\nimport type { NodeType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nexport const findListItemPos = (typeOrName: string | NodeType, state: EditorState) => {\n  const { $from } = state.selection\n  const nodeType = getNodeType(typeOrName, state.schema)\n\n  let currentNode = null\n  let currentDepth = $from.depth\n  let currentPos = $from.pos\n  let targetDepth: number | null = null\n\n  while (currentDepth > 0 && targetDepth === null) {\n    currentNode = $from.node(currentDepth)\n\n    if (currentNode.type === nodeType) {\n      targetDepth = currentDepth\n    } else {\n      currentDepth -= 1\n      currentPos -= 1\n    }\n  }\n\n  if (targetDepth === null) {\n    return null\n  }\n\n  return { $pos: state.doc.resolve(currentPos), depth: targetDepth }\n}\n", "import { getNodeAtPosition } from '@tiptap/core'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\n\nexport const getNextListDepth = (typeOrName: string, state: EditorState) => {\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos) {\n    return false\n  }\n\n  const [, depth] = getNodeAtPosition(state, typeOrName, listItemPos.$pos.pos + 4)\n\n  return depth\n}\n", "import type { Editor } from '@tiptap/core'\nimport { isAtStartOfNode, isNodeActive } from '@tiptap/core'\nimport type { Node } from '@tiptap/pm/model'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { hasListBefore } from './hasListBefore.js'\nimport { hasListItemBefore } from './hasListItemBefore.js'\nimport { listItemHasSubList } from './listItemHasSubList.js'\n\nexport const handleBackspace = (editor: Editor, name: string, parentListTypes: string[]) => {\n  // this is required to still handle the undo handling\n  if (editor.commands.undoInputRule()) {\n    return true\n  }\n\n  // if the selection is not collapsed\n  // we can rely on the default backspace behavior\n  if (editor.state.selection.from !== editor.state.selection.to) {\n    return false\n  }\n\n  // if the current item is NOT inside a list item &\n  // the previous item is a list (orderedList or bulletList)\n  // move the cursor into the list and delete the current item\n  if (!isNodeActive(editor.state, name) && hasListBefore(editor.state, name, parentListTypes)) {\n    const { $anchor } = editor.state.selection\n\n    const $listPos = editor.state.doc.resolve($anchor.before() - 1)\n\n    const listDescendants: Array<{ node: Node; pos: number }> = []\n\n    $listPos.node().descendants((node, pos) => {\n      if (node.type.name === name) {\n        listDescendants.push({ node, pos })\n      }\n    })\n\n    const lastItem = listDescendants.at(-1)\n\n    if (!lastItem) {\n      return false\n    }\n\n    const $lastItemPos = editor.state.doc.resolve($listPos.start() + lastItem.pos + 1)\n\n    return editor\n      .chain()\n      .cut({ from: $anchor.start() - 1, to: $anchor.end() + 1 }, $lastItemPos.end())\n      .joinForward()\n      .run()\n  }\n\n  // if the cursor is not inside the current node type\n  // do nothing and proceed\n  if (!isNodeActive(editor.state, name)) {\n    return false\n  }\n\n  // if the cursor is not at the start of a node\n  // do nothing and proceed\n  if (!isAtStartOfNode(editor.state)) {\n    return false\n  }\n\n  const listItemPos = findListItemPos(name, editor.state)\n\n  if (!listItemPos) {\n    return false\n  }\n\n  const $prev = editor.state.doc.resolve(listItemPos.$pos.pos - 2)\n  const prevNode = $prev.node(listItemPos.depth)\n\n  const previousListItemHasSubList = listItemHasSubList(name, editor.state, prevNode)\n\n  // if the previous item is a list item and doesn't have a sublist, join the list items\n  if (hasListItemBefore(name, editor.state) && !previousListItemHasSubList) {\n    return editor.commands.joinItemBackward()\n  }\n\n  // otherwise in the end, a backspace should\n  // always just lift the list item if\n  // joining / merging is not possible\n  return editor.chain().liftListItem(name).run()\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListBefore = (editorState: EditorState, name: string, parentListTypes: string[]) => {\n  const { $anchor } = editorState.selection\n\n  const previousNodePos = Math.max(0, $anchor.pos - 2)\n\n  const previousNode = editorState.doc.resolve(previousNodePos).node()\n\n  if (!previousNode || !parentListTypes.includes(previousNode.type.name)) {\n    return false\n  }\n\n  return true\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListItemBefore = (typeOrName: string, state: EditorState): boolean => {\n  const { $anchor } = state.selection\n\n  const $targetPos = state.doc.resolve($anchor.pos - 2)\n\n  if ($targetPos.index() === 0) {\n    return false\n  }\n\n  if ($targetPos.nodeBefore?.type.name !== typeOrName) {\n    return false\n  }\n\n  return true\n}\n", "import { getNodeType } from '@tiptap/core'\nimport type { Node } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nexport const listItemHasSubList = (typeOrName: string, state: EditorState, node?: Node) => {\n  if (!node) {\n    return false\n  }\n\n  const nodeType = getNodeType(typeOrName, state.schema)\n\n  let hasSubList = false\n\n  node.descendants(child => {\n    if (child.type === nodeType) {\n      hasSubList = true\n    }\n  })\n\n  return hasSubList\n}\n", "import type { Editor } from '@tiptap/core'\nimport { isAtEndOfNode, isNodeActive } from '@tiptap/core'\n\nimport { nextListIsDeeper } from './nextListIsDeeper.js'\nimport { nextListIsHigher } from './nextListIsHigher.js'\n\nexport const handleDelete = (editor: Editor, name: string) => {\n  // if the cursor is not inside the current node type\n  // do nothing and proceed\n  if (!isNodeActive(editor.state, name)) {\n    return false\n  }\n\n  // if the cursor is not at the end of a node\n  // do nothing and proceed\n  if (!isAtEndOfNode(editor.state, name)) {\n    return false\n  }\n\n  // if the selection is not collapsed, or not within a single node\n  // do nothing and proceed\n  const { selection } = editor.state\n  const { $from, $to } = selection\n\n  if (!selection.empty && $from.sameParent($to)) {\n    return false\n  }\n\n  // check if the next node is a list with a deeper depth\n  if (nextListIsDeeper(name, editor.state)) {\n    return editor\n      .chain()\n      .focus(editor.state.selection.from + 4)\n      .lift(name)\n      .joinBackward()\n      .run()\n  }\n\n  if (nextList<PERSON>s<PERSON>igh<PERSON>(name, editor.state)) {\n    return editor.chain().joinForward().joinBackward().run()\n  }\n\n  return editor.commands.joinItemForward()\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { getNextListDepth } from './getNextListDepth.js'\n\nexport const nextListIsDeeper = (typeOrName: string, state: EditorState) => {\n  const listDepth = getNextListDepth(typeOrName, state)\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos || !listDepth) {\n    return false\n  }\n\n  if (listDepth > listItemPos.depth) {\n    return true\n  }\n\n  return false\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { getNextListDepth } from './getNextListDepth.js'\n\nexport const nextListIsHigher = (typeOrName: string, state: EditorState) => {\n  const listDepth = getNextListDepth(typeOrName, state)\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos || !listDepth) {\n    return false\n  }\n\n  if (listDepth < listItemPos.depth) {\n    return true\n  }\n\n  return false\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListItemAfter = (typeOrName: string, state: EditorState): boolean => {\n  const { $anchor } = state.selection\n\n  const $targetPos = state.doc.resolve($anchor.pos - $anchor.parentOffset - 2)\n\n  if ($targetPos.index() === $targetPos.parent.childCount - 1) {\n    return false\n  }\n\n  if ($targetPos.nodeAfter?.type.name !== typeOrName) {\n    return false\n  }\n\n  return true\n}\n", "import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface OrderedListOptions {\n  /**\n   * The node type name for list items.\n   * @default 'listItem'\n   * @example 'myListItem'\n   */\n  itemTypeName: string\n\n  /**\n   * The HTML attributes for an ordered list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * Keep the marks when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean\n\n  /**\n   * Keep the attributes when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    orderedList: {\n      /**\n       * Toggle an ordered list\n       * @example editor.commands.toggleOrderedList()\n       */\n      toggleOrderedList: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nexport const orderedListInputRegex = /^(\\d+)\\.\\s$/\n\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const OrderedList = Node.create<OrderedListOptions>({\n  name: 'orderedList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  addAttributes() {\n    return {\n      start: {\n        default: 1,\n        parseHTML: element => {\n          return element.hasAttribute('start') ? parseInt(element.getAttribute('start') || '', 10) : 1\n        },\n      },\n      type: {\n        default: null,\n        parseHTML: element => element.getAttribute('type'),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'ol',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const { start, ...attributesWithoutStart } = HTMLAttributes\n\n    return start === 1\n      ? ['ol', mergeAttributes(this.options.HTMLAttributes, attributesWithoutStart), 0]\n      : ['ol', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleOrderedList:\n        () =>\n        ({ commands, chain }) => {\n          if (this.options.keepAttributes) {\n            return chain()\n              .toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n              .updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName))\n              .run()\n          }\n          return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: orderedListInputRegex,\n      type: this.type,\n      getAttributes: match => ({ start: +match[1] }),\n      joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: orderedListInputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n        joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        editor: this.editor,\n      })\n    }\n    return [inputRule]\n  },\n})\n", "import type { KeyboardShortcutCommand } from '@tiptap/core'\nimport { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\nimport type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nexport interface TaskItemOptions {\n  /**\n   * A callback function that is called when the checkbox is clicked while the editor is in readonly mode.\n   * @param node The prosemirror node of the task item\n   * @param checked The new checked state\n   * @returns boolean\n   */\n  onReadOnlyChecked?: (node: ProseMirrorNode, checked: boolean) => boolean\n\n  /**\n   * Controls whether the task items can be nested or not.\n   * @default false\n   * @example true\n   */\n  nested: boolean\n\n  /**\n   * HTML attributes to add to the task item element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * The node type for taskList nodes\n   * @default 'taskList'\n   * @example 'myCustomTaskList'\n   */\n  taskListTypeName: string\n\n  /**\n   * Accessibility options for the task item.\n   * @default {}\n   * @example\n   * ```js\n   * {\n   *   checkboxLabel: (node) => `Task item: ${node.textContent || 'empty task item'}`\n   * }\n   */\n  a11y?: {\n    checkboxLabel?: (node: ProseMirrorNode, checked: boolean) => string\n  }\n}\n\n/**\n * Matches a task item to a - [ ] on input.\n */\nexport const inputRegex = /^\\s*(\\[([( |x])?\\])\\s$/\n\n/**\n * This extension allows you to create task items.\n * @see https://www.tiptap.dev/api/nodes/task-item\n */\nexport const TaskItem = Node.create<TaskItemOptions>({\n  name: 'taskItem',\n\n  addOptions() {\n    return {\n      nested: false,\n      HTMLAttributes: {},\n      taskListTypeName: 'taskList',\n      a11y: undefined,\n    }\n  },\n\n  content() {\n    return this.options.nested ? 'paragraph block*' : 'paragraph+'\n  },\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      checked: {\n        default: false,\n        keepOnSplit: false,\n        parseHTML: element => {\n          const dataChecked = element.getAttribute('data-checked')\n\n          return dataChecked === '' || dataChecked === 'true'\n        },\n        renderHTML: attributes => ({\n          'data-checked': attributes.checked,\n        }),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `li[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'li',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {\n        'data-type': this.name,\n      }),\n      [\n        'label',\n        [\n          'input',\n          {\n            type: 'checkbox',\n            checked: node.attrs.checked ? 'checked' : null,\n          },\n        ],\n        ['span'],\n      ],\n      ['div', 0],\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    const shortcuts: {\n      [key: string]: KeyboardShortcutCommand\n    } = {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n\n    if (!this.options.nested) {\n      return shortcuts\n    }\n\n    return {\n      ...shortcuts,\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n    }\n  },\n\n  addNodeView() {\n    return ({ node, HTMLAttributes, getPos, editor }) => {\n      const listItem = document.createElement('li')\n      const checkboxWrapper = document.createElement('label')\n      const checkboxStyler = document.createElement('span')\n      const checkbox = document.createElement('input')\n      const content = document.createElement('div')\n\n      const updateA11Y = (currentNode: ProseMirrorNode) => {\n        checkbox.ariaLabel =\n          this.options.a11y?.checkboxLabel?.(currentNode, checkbox.checked) ||\n          `Task item checkbox for ${currentNode.textContent || 'empty task item'}`\n      }\n\n      updateA11Y(node)\n\n      checkboxWrapper.contentEditable = 'false'\n      checkbox.type = 'checkbox'\n      checkbox.addEventListener('mousedown', event => event.preventDefault())\n      checkbox.addEventListener('change', event => {\n        // if the editor isn’t editable and we don't have a handler for\n        // readonly checks we have to undo the latest change\n        if (!editor.isEditable && !this.options.onReadOnlyChecked) {\n          checkbox.checked = !checkbox.checked\n\n          return\n        }\n\n        const { checked } = event.target as any\n\n        if (editor.isEditable && typeof getPos === 'function') {\n          editor\n            .chain()\n            .focus(undefined, { scrollIntoView: false })\n            .command(({ tr }) => {\n              const position = getPos()\n\n              if (typeof position !== 'number') {\n                return false\n              }\n              const currentNode = tr.doc.nodeAt(position)\n\n              tr.setNodeMarkup(position, undefined, {\n                ...currentNode?.attrs,\n                checked,\n              })\n\n              return true\n            })\n            .run()\n        }\n        if (!editor.isEditable && this.options.onReadOnlyChecked) {\n          // Reset state if onReadOnlyChecked returns false\n          if (!this.options.onReadOnlyChecked(node, checked)) {\n            checkbox.checked = !checkbox.checked\n          }\n        }\n      })\n\n      Object.entries(this.options.HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      listItem.dataset.checked = node.attrs.checked\n      checkbox.checked = node.attrs.checked\n\n      checkboxWrapper.append(checkbox, checkboxStyler)\n      listItem.append(checkboxWrapper, content)\n\n      Object.entries(HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      return {\n        dom: listItem,\n        contentDOM: content,\n        update: updatedNode => {\n          if (updatedNode.type !== this.type) {\n            return false\n          }\n\n          listItem.dataset.checked = updatedNode.attrs.checked\n          checkbox.checked = updatedNode.attrs.checked\n          updateA11Y(updatedNode)\n\n          return true\n        },\n      }\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          checked: match[match.length - 1] === 'x',\n        }),\n      }),\n    ]\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TaskListOptions {\n  /**\n   * The node type name for a task item.\n   * @default 'taskItem'\n   * @example 'myCustomTaskItem'\n   */\n  itemTypeName: string\n\n  /**\n   * The HTML attributes for a task list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    taskList: {\n      /**\n       * Toggle a task list\n       * @example editor.commands.toggleTaskList()\n       */\n      toggleTaskList: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create task lists.\n * @see https://www.tiptap.dev/api/nodes/task-list\n */\nexport const TaskList = Node.create<TaskListOptions>({\n  name: 'taskList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'taskItem',\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `ul[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, { 'data-type': this.name }), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleTaskList:\n        () =>\n        ({ commands }) => {\n          return commands.toggleList(this.name, this.options.itemTypeName)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-9': () => this.editor.commands.toggleTaskList(),\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,gBAA0B;;;ACA1B,kBAAyD;AAEzD,IAAM,eAAe;AACrB,IAAM,gBAAgB;AA8Cf,IAAM,uBAAuB;AAQ7B,IAAM,aAAa,iBAAK,OAA0B;AAAA,EACvD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,cAAc;AAAA,MACd,gBAAgB,CAAC;AAAA,MACjB,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,EAEP,UAAU;AACR,WAAO,GAAG,KAAK,QAAQ,YAAY;AAAA,EACrC;AAAA,EAEA,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EACvB;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,UAAM,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC/E;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,kBACE,MACA,CAAC,EAAE,UAAU,MAAM,MAAM;AACvB,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAM,EACV,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EACvE,iBAAiB,cAAc,KAAK,OAAO,cAAc,aAAa,CAAC,EACvE,IAAI;AAAA,QACT;AACA,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;AAAA,MACzF;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAiB;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,QAAI,gBAAY,+BAAkB;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,IACb,CAAC;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,sBAAY,+BAAkB;AAAA,QAC5B,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,WAAW,KAAK,QAAQ;AAAA,QACxB,gBAAgB,KAAK,QAAQ;AAAA,QAC7B,eAAe,MAAM;AACnB,iBAAO,KAAK,OAAO,cAAc,aAAa;AAAA,QAChD;AAAA,QACA,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AACA,WAAO,CAAC,SAAS;AAAA,EACnB;AACF,CAAC;;;AC7HD,IAAAC,eAAsC;AA6B/B,IAAM,WAAW,kBAAK,OAAwB;AAAA,EACnD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,MACjB,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACvB;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,EAET,UAAU;AAAA,EAEV,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,UAAM,8BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC/E;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,OAAO,MAAM,KAAK,OAAO,SAAS,cAAc,KAAK,IAAI;AAAA,MACzD,KAAK,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;AAAA,MACtD,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;AAAA,IAChE;AAAA,EACF;AACF,CAAC;;;AC/DD,IAAAC,eAA0B;;;ACA1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,eAA4B;AAIrB,IAAM,kBAAkB,CAAC,YAA+B,UAAuB;AACpF,QAAM,EAAE,MAAM,IAAI,MAAM;AACxB,QAAM,eAAW,0BAAY,YAAY,MAAM,MAAM;AAErD,MAAI,cAAc;AAClB,MAAI,eAAe,MAAM;AACzB,MAAI,aAAa,MAAM;AACvB,MAAI,cAA6B;AAEjC,SAAO,eAAe,KAAK,gBAAgB,MAAM;AAC/C,kBAAc,MAAM,KAAK,YAAY;AAErC,QAAI,YAAY,SAAS,UAAU;AACjC,oBAAc;AAAA,IAChB,OAAO;AACL,sBAAgB;AAChB,oBAAc;AAAA,IAChB;AAAA,EACF;AAEA,MAAI,gBAAgB,MAAM;AACxB,WAAO;AAAA,EACT;AAEA,SAAO,EAAE,MAAM,MAAM,IAAI,QAAQ,UAAU,GAAG,OAAO,YAAY;AACnE;;;AC7BA,IAAAC,eAAkC;AAK3B,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,CAAC,EAAE,KAAK,QAAI,gCAAkB,OAAO,YAAY,YAAY,KAAK,MAAM,CAAC;AAE/E,SAAO;AACT;;;ACdA,IAAAC,eAA8C;;;ACCvC,IAAM,gBAAgB,CAAC,aAA0B,MAAc,oBAA8B;AAClG,QAAM,EAAE,QAAQ,IAAI,YAAY;AAEhC,QAAM,kBAAkB,KAAK,IAAI,GAAG,QAAQ,MAAM,CAAC;AAEnD,QAAM,eAAe,YAAY,IAAI,QAAQ,eAAe,EAAE,KAAK;AAEnE,MAAI,CAAC,gBAAgB,CAAC,gBAAgB,SAAS,aAAa,KAAK,IAAI,GAAG;AACtE,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACZO,IAAM,oBAAoB,CAAC,YAAoB,UAAgC;AAFtF;AAGE,QAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,QAAM,aAAa,MAAM,IAAI,QAAQ,QAAQ,MAAM,CAAC;AAEpD,MAAI,WAAW,MAAM,MAAM,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,QAAI,gBAAW,eAAX,mBAAuB,KAAK,UAAS,YAAY;AACnD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AChBA,IAAAC,eAA4B;AAIrB,IAAM,qBAAqB,CAAC,YAAoB,OAAoB,SAAgB;AACzF,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,QAAM,eAAW,0BAAY,YAAY,MAAM,MAAM;AAErD,MAAI,aAAa;AAEjB,OAAK,YAAY,WAAS;AACxB,QAAI,MAAM,SAAS,UAAU;AAC3B,mBAAa;AAAA,IACf;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;AHXO,IAAM,kBAAkB,CAAC,QAAgB,MAAc,oBAA8B;AAE1F,MAAI,OAAO,SAAS,cAAc,GAAG;AACnC,WAAO;AAAA,EACT;AAIA,MAAI,OAAO,MAAM,UAAU,SAAS,OAAO,MAAM,UAAU,IAAI;AAC7D,WAAO;AAAA,EACT;AAKA,MAAI,KAAC,2BAAa,OAAO,OAAO,IAAI,KAAK,cAAc,OAAO,OAAO,MAAM,eAAe,GAAG;AAC3F,UAAM,EAAE,QAAQ,IAAI,OAAO,MAAM;AAEjC,UAAM,WAAW,OAAO,MAAM,IAAI,QAAQ,QAAQ,OAAO,IAAI,CAAC;AAE9D,UAAM,kBAAsD,CAAC;AAE7D,aAAS,KAAK,EAAE,YAAY,CAAC,MAAM,QAAQ;AACzC,UAAI,KAAK,KAAK,SAAS,MAAM;AAC3B,wBAAgB,KAAK,EAAE,MAAM,IAAI,CAAC;AAAA,MACpC;AAAA,IACF,CAAC;AAED,UAAM,WAAW,gBAAgB,GAAG,EAAE;AAEtC,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,OAAO,MAAM,IAAI,QAAQ,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC;AAEjF,WAAO,OACJ,MAAM,EACN,IAAI,EAAE,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG,aAAa,IAAI,CAAC,EAC5E,YAAY,EACZ,IAAI;AAAA,EACT;AAIA,MAAI,KAAC,2BAAa,OAAO,OAAO,IAAI,GAAG;AACrC,WAAO;AAAA,EACT;AAIA,MAAI,KAAC,8BAAgB,OAAO,KAAK,GAAG;AAClC,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,gBAAgB,MAAM,OAAO,KAAK;AAEtD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,OAAO,MAAM,IAAI,QAAQ,YAAY,KAAK,MAAM,CAAC;AAC/D,QAAM,WAAW,MAAM,KAAK,YAAY,KAAK;AAE7C,QAAM,6BAA6B,mBAAmB,MAAM,OAAO,OAAO,QAAQ;AAGlF,MAAI,kBAAkB,MAAM,OAAO,KAAK,KAAK,CAAC,4BAA4B;AACxE,WAAO,OAAO,SAAS,iBAAiB;AAAA,EAC1C;AAKA,SAAO,OAAO,MAAM,EAAE,aAAa,IAAI,EAAE,IAAI;AAC/C;;;AInFA,IAAAC,eAA4C;;;ACIrC,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,YAAY,iBAAiB,YAAY,KAAK;AACpD,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,YAAY,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACbO,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,YAAY,iBAAiB,YAAY,KAAK;AACpD,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,YAAY,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AFZO,IAAM,eAAe,CAAC,QAAgB,SAAiB;AAG5D,MAAI,KAAC,2BAAa,OAAO,OAAO,IAAI,GAAG;AACrC,WAAO;AAAA,EACT;AAIA,MAAI,KAAC,4BAAc,OAAO,OAAO,IAAI,GAAG;AACtC,WAAO;AAAA,EACT;AAIA,QAAM,EAAE,UAAU,IAAI,OAAO;AAC7B,QAAM,EAAE,OAAO,IAAI,IAAI;AAEvB,MAAI,CAAC,UAAU,SAAS,MAAM,WAAW,GAAG,GAAG;AAC7C,WAAO;AAAA,EACT;AAGA,MAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG;AACxC,WAAO,OACJ,MAAM,EACN,MAAM,OAAO,MAAM,UAAU,OAAO,CAAC,EACrC,KAAK,IAAI,EACT,aAAa,EACb,IAAI;AAAA,EACT;AAEA,MAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG;AACxC,WAAO,OAAO,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI;AAAA,EACzD;AAEA,SAAO,OAAO,SAAS,gBAAgB;AACzC;;;AGzCO,IAAM,mBAAmB,CAAC,YAAoB,UAAgC;AAFrF;AAGE,QAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,QAAM,aAAa,MAAM,IAAI,QAAQ,QAAQ,MAAM,QAAQ,eAAe,CAAC;AAE3E,MAAI,WAAW,MAAM,MAAM,WAAW,OAAO,aAAa,GAAG;AAC3D,WAAO;AAAA,EACT;AAEA,QAAI,gBAAW,cAAX,mBAAsB,KAAK,UAAS,YAAY;AAClD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AXOO,IAAM,aAAa,uBAAU,OAA0B;AAAA,EAC5D,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,WAAW;AAAA,QACT;AAAA,UACE,UAAU;AAAA,UACV,cAAc,CAAC,cAAc,aAAa;AAAA,QAC5C;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,cAAc,CAAC,UAAU;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,QAAQ,CAAC,EAAE,OAAO,MAAM;AACtB,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,SAAS,MAAM;AAC/C,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,aAAa,QAAQ,QAAQ,GAAG;AAClC,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MACA,cAAc,CAAC,EAAE,OAAO,MAAM;AAC5B,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,SAAS,MAAM;AAC/C,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,aAAa,QAAQ,QAAQ,GAAG;AAClC,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MACA,WAAW,CAAC,EAAE,OAAO,MAAM;AACzB,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,UAAU,aAAa,MAAM;AAC7D,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,gBAAgB,QAAQ,UAAU,YAAY,GAAG;AACnD,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB,CAAC,EAAE,OAAO,MAAM;AAC/B,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,UAAU,aAAa,MAAM;AAC7D,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,gBAAgB,QAAQ,UAAU,YAAY,GAAG;AACnD,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF,CAAC;;;AYzGD,IAAAC,eAAyD;AAEzD,IAAMC,gBAAe;AACrB,IAAMC,iBAAgB;AA+Cf,IAAM,wBAAwB;AAQ9B,IAAM,cAAc,kBAAK,OAA2B;AAAA,EACzD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,cAAc;AAAA,MACd,gBAAgB,CAAC;AAAA,MACjB,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,EAEP,UAAU;AACR,WAAO,GAAG,KAAK,QAAQ,YAAY;AAAA,EACrC;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,MACL,OAAO;AAAA,QACL,SAAS;AAAA,QACT,WAAW,aAAW;AACpB,iBAAO,QAAQ,aAAa,OAAO,IAAI,SAAS,QAAQ,aAAa,OAAO,KAAK,IAAI,EAAE,IAAI;AAAA,QAC7F;AAAA,MACF;AAAA,MACA,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW,aAAW,QAAQ,aAAa,MAAM;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,UAAM,EAAE,OAAO,GAAG,uBAAuB,IAAI;AAE7C,WAAO,UAAU,IACb,CAAC,UAAM,8BAAgB,KAAK,QAAQ,gBAAgB,sBAAsB,GAAG,CAAC,IAC9E,CAAC,UAAM,8BAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC5E;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,mBACE,MACA,CAAC,EAAE,UAAU,MAAM,MAAM;AACvB,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAM,EACV,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EACvE,iBAAiBD,eAAc,KAAK,OAAO,cAAcC,cAAa,CAAC,EACvE,IAAI;AAAA,QACT;AACA,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;AAAA,MACzF;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,eAAe,MAAM,KAAK,OAAO,SAAS,kBAAkB;AAAA,IAC9D;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,QAAI,gBAAY,gCAAkB;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,MACX,eAAe,YAAU,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;AAAA,MAC5C,eAAe,CAAC,OAAO,SAAS,KAAK,aAAa,KAAK,MAAM,UAAU,CAAC,MAAM,CAAC;AAAA,IACjF,CAAC;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,sBAAY,gCAAkB;AAAA,QAC5B,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,WAAW,KAAK,QAAQ;AAAA,QACxB,gBAAgB,KAAK,QAAQ;AAAA,QAC7B,eAAe,YAAU,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,OAAO,cAAcA,cAAa,EAAE;AAAA,QACzF,eAAe,CAAC,OAAO,SAAS,KAAK,aAAa,KAAK,MAAM,UAAU,CAAC,MAAM,CAAC;AAAA,QAC/E,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AACA,WAAO,CAAC,SAAS;AAAA,EACnB;AACF,CAAC;;;ACrJD,IAAAC,gBAAyD;AAkDlD,IAAM,aAAa;AAMnB,IAAM,WAAW,mBAAK,OAAwB;AAAA,EACnD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,gBAAgB,CAAC;AAAA,MACjB,kBAAkB;AAAA,MAClB,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,QAAQ,SAAS,qBAAqB;AAAA,EACpD;AAAA,EAEA,UAAU;AAAA,EAEV,gBAAgB;AACd,WAAO;AAAA,MACL,SAAS;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,WAAW,aAAW;AACpB,gBAAM,cAAc,QAAQ,aAAa,cAAc;AAEvD,iBAAO,gBAAgB,MAAM,gBAAgB;AAAA,QAC/C;AAAA,QACA,YAAY,iBAAe;AAAA,UACzB,gBAAgB,WAAW;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK,iBAAiB,KAAK,IAAI;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,MAAM,eAAe,GAAG;AACnC,WAAO;AAAA,MACL;AAAA,UACA,+BAAgB,KAAK,QAAQ,gBAAgB,gBAAgB;AAAA,QAC3D,aAAa,KAAK;AAAA,MACpB,CAAC;AAAA,MACD;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS,KAAK,MAAM,UAAU,YAAY;AAAA,UAC5C;AAAA,QACF;AAAA,QACA,CAAC,MAAM;AAAA,MACT;AAAA,MACA,CAAC,OAAO,CAAC;AAAA,IACX;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,UAAM,YAEF;AAAA,MACF,OAAO,MAAM,KAAK,OAAO,SAAS,cAAc,KAAK,IAAI;AAAA,MACzD,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;AAAA,IAChE;AAEA,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,KAAK,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,cAAc;AACZ,WAAO,CAAC,EAAE,MAAM,gBAAgB,QAAQ,OAAO,MAAM;AACnD,YAAM,WAAW,SAAS,cAAc,IAAI;AAC5C,YAAM,kBAAkB,SAAS,cAAc,OAAO;AACtD,YAAM,iBAAiB,SAAS,cAAc,MAAM;AACpD,YAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,YAAM,UAAU,SAAS,cAAc,KAAK;AAE5C,YAAM,aAAa,CAAC,gBAAiC;AApJ3D;AAqJQ,iBAAS,cACP,gBAAK,QAAQ,SAAb,mBAAmB,kBAAnB,4BAAmC,aAAa,SAAS,aACzD,0BAA0B,YAAY,eAAe,iBAAiB;AAAA,MAC1E;AAEA,iBAAW,IAAI;AAEf,sBAAgB,kBAAkB;AAClC,eAAS,OAAO;AAChB,eAAS,iBAAiB,aAAa,WAAS,MAAM,eAAe,CAAC;AACtE,eAAS,iBAAiB,UAAU,WAAS;AAG3C,YAAI,CAAC,OAAO,cAAc,CAAC,KAAK,QAAQ,mBAAmB;AACzD,mBAAS,UAAU,CAAC,SAAS;AAE7B;AAAA,QACF;AAEA,cAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,YAAI,OAAO,cAAc,OAAO,WAAW,YAAY;AACrD,iBACG,MAAM,EACN,MAAM,QAAW,EAAE,gBAAgB,MAAM,CAAC,EAC1C,QAAQ,CAAC,EAAE,GAAG,MAAM;AACnB,kBAAM,WAAW,OAAO;AAExB,gBAAI,OAAO,aAAa,UAAU;AAChC,qBAAO;AAAA,YACT;AACA,kBAAM,cAAc,GAAG,IAAI,OAAO,QAAQ;AAE1C,eAAG,cAAc,UAAU,QAAW;AAAA,cACpC,GAAG,2CAAa;AAAA,cAChB;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT,CAAC,EACA,IAAI;AAAA,QACT;AACA,YAAI,CAAC,OAAO,cAAc,KAAK,QAAQ,mBAAmB;AAExD,cAAI,CAAC,KAAK,QAAQ,kBAAkB,MAAM,OAAO,GAAG;AAClD,qBAAS,UAAU,CAAC,SAAS;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO,QAAQ,KAAK,QAAQ,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpE,iBAAS,aAAa,KAAK,KAAK;AAAA,MAClC,CAAC;AAED,eAAS,QAAQ,UAAU,KAAK,MAAM;AACtC,eAAS,UAAU,KAAK,MAAM;AAE9B,sBAAgB,OAAO,UAAU,cAAc;AAC/C,eAAS,OAAO,iBAAiB,OAAO;AAExC,aAAO,QAAQ,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACvD,iBAAS,aAAa,KAAK,KAAK;AAAA,MAClC,CAAC;AAED,aAAO;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ,iBAAe;AACrB,cAAI,YAAY,SAAS,KAAK,MAAM;AAClC,mBAAO;AAAA,UACT;AAEA,mBAAS,QAAQ,UAAU,YAAY,MAAM;AAC7C,mBAAS,UAAU,YAAY,MAAM;AACrC,qBAAW,WAAW;AAEtB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,UACL,iCAAkB;AAAA,QAChB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,eAAe,YAAU;AAAA,UACvB,SAAS,MAAM,MAAM,SAAS,CAAC,MAAM;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AClPD,IAAAC,gBAAsC;AAkC/B,IAAM,WAAW,mBAAK,OAAwB;AAAA,EACnD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,cAAc;AAAA,MACd,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,EAEP,UAAU;AACR,WAAO,GAAG,KAAK,QAAQ,YAAY;AAAA,EACrC;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK,iBAAiB,KAAK,IAAI;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,UAAM,+BAAgB,KAAK,QAAQ,gBAAgB,gBAAgB,EAAE,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC;AAAA,EAC3G;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,gBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,YAAY;AAAA,MACjE;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,eAAe,MAAM,KAAK,OAAO,SAAS,eAAe;AAAA,IAC3D;AAAA,EACF;AACF,CAAC;;;AjB9BM,IAAM,UAAU,wBAAU,OAAuB;AAAA,EACtD,MAAM;AAAA,EAEN,gBAAgB;AACd,UAAM,aAAa,CAAC;AAEpB,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC/D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA,IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;AAAA,IAC/D;AAEA,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,iBAAW,KAAK,YAAY,UAAU,KAAK,QAAQ,WAAW,CAAC;AAAA,IACjE;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA,IAC3D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;AAAA,IAC3D;AAEA,WAAO;AAAA,EACT;AACF,CAAC;", "names": ["import_core", "import_core", "import_core", "import_core", "import_core", "import_core", "import_core", "import_core", "import_core", "ListItemName", "TextStyleName", "import_core", "import_core"]}