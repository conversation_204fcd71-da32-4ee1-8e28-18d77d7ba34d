{"name": "@tiptap/extensions", "description": "various extensions for tiptap", "version": "3.3.0", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap extension"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "type": "module", "exports": {".": {"types": {"import": "./dist/index.d.ts", "require": "./dist/index.d.cts"}, "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./character-count": {"types": {"import": "./dist/character-count/index.d.ts", "require": "./dist/character-count/index.d.cts"}, "import": "./dist/character-count/index.js", "require": "./dist/character-count/index.cjs"}, "./drop-cursor": {"types": {"import": "./dist/drop-cursor/index.d.ts", "require": "./dist/drop-cursor/index.d.cts"}, "import": "./dist/drop-cursor/index.js", "require": "./dist/drop-cursor/index.cjs"}, "./focus": {"types": {"import": "./dist/focus/index.d.ts", "require": "./dist/focus/index.d.cts"}, "import": "./dist/focus/index.js", "require": "./dist/focus/index.cjs"}, "./gap-cursor": {"types": {"import": "./dist/gap-cursor/index.d.ts", "require": "./dist/gap-cursor/index.d.cts"}, "import": "./dist/gap-cursor/index.js", "require": "./dist/gap-cursor/index.cjs"}, "./undo-redo": {"types": {"import": "./dist/undo-redo/index.d.ts", "require": "./dist/undo-redo/index.d.cts"}, "import": "./dist/undo-redo/index.js", "require": "./dist/undo-redo/index.cjs"}, "./placeholder": {"types": {"import": "./dist/placeholder/index.d.ts", "require": "./dist/placeholder/index.d.cts"}, "import": "./dist/placeholder/index.js", "require": "./dist/placeholder/index.cjs"}, "./selection": {"types": {"import": "./dist/selection/index.d.ts", "require": "./dist/selection/index.d.cts"}, "import": "./dist/selection/index.js", "require": "./dist/selection/index.cjs"}, "./trailing-node": {"types": {"import": "./dist/trailing-node/index.d.ts", "require": "./dist/trailing-node/index.d.cts"}, "import": "./dist/trailing-node/index.js", "require": "./dist/trailing-node/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["src", "dist"], "devDependencies": {"@tiptap/core": "^3.3.0", "@tiptap/pm": "^3.3.0"}, "peerDependencies": {"@tiptap/core": "^3.3.0", "@tiptap/pm": "^3.3.0"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/extension"}, "scripts": {"build": "tsup", "lint": "prettier ./src/ --check && eslint --cache --quiet --no-error-on-unmatched-pattern ./src/"}}