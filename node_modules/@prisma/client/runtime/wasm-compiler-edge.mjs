var oc=Object.create;var Jr=Object.defineProperty;var sc=Object.getOwnPropertyDescriptor;var ac=Object.getOwnPropertyNames;var lc=Object.getPrototypeOf,uc=Object.prototype.hasOwnProperty;var ge=(e,t)=>()=>(e&&(t=e(e=0)),t);var ie=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ht=(e,t)=>{for(var r in t)Jr(e,r,{get:t[r],enumerable:!0})},Fo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of ac(t))!uc.call(e,i)&&i!==r&&Jr(e,i,{get:()=>t[i],enumerable:!(n=sc(t,i))||n.enumerable});return e};var Ae=(e,t,r)=>(r=e!=null?oc(lc(e)):{},Fo(t||!e||!e.__esModule?Jr(r,"default",{value:e,enumerable:!0}):r,e)),cc=e=>Fo(Jr({},"__esModule",{value:!0}),e);function si(e,t){if(t=t.toLowerCase(),t==="utf8"||t==="utf-8")return new h(fc.encode(e));if(t==="base64"||t==="base64url")return e=e.replace(/-/g,"+").replace(/_/g,"/"),e=e.replace(/[^A-Za-z0-9+/]/g,""),new h([...atob(e)].map(r=>r.charCodeAt(0)));if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return new h([...e].map(r=>r.charCodeAt(0)));if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let r=new h(e.length*2),n=new DataView(r.buffer);for(let i=0;i<e.length;i++)n.setUint16(i*2,e.charCodeAt(i),!0);return r}if(t==="hex"){let r=new h(e.length/2);for(let n=0,i=0;i<e.length;i+=2,n++)r[n]=parseInt(e.slice(i,i+2),16);return r}$o(`encoding "${t}"`)}function pc(e){let r=Object.getOwnPropertyNames(DataView.prototype).filter(a=>a.startsWith("get")||a.startsWith("set")),n=r.map(a=>a.replace("get","read").replace("set","write")),i=(a,f)=>function(w=0){return Y(w,"offset"),pe(w,"offset"),X(w,"offset",this.length-1),new DataView(this.buffer)[r[a]](w,f)},o=(a,f)=>function(w,A=0){let C=r[a].match(/set(\w+\d+)/)[1].toLowerCase(),I=dc[C];return Y(A,"offset"),pe(A,"offset"),X(A,"offset",this.length-1),mc(w,"value",I[0],I[1]),new DataView(this.buffer)[r[a]](A,w,f),A+parseInt(r[a].match(/\d+/)[0])/8},s=a=>{a.forEach(f=>{f.includes("Uint")&&(e[f.replace("Uint","UInt")]=e[f]),f.includes("Float64")&&(e[f.replace("Float64","Double")]=e[f]),f.includes("Float32")&&(e[f.replace("Float32","Float")]=e[f])})};n.forEach((a,f)=>{a.startsWith("read")&&(e[a]=i(f,!1),e[a+"LE"]=i(f,!0),e[a+"BE"]=i(f,!1)),a.startsWith("write")&&(e[a]=o(f,!1),e[a+"LE"]=o(f,!0),e[a+"BE"]=o(f,!1)),s([a,a+"LE",a+"BE"])})}function $o(e){throw new Error(`Buffer polyfill does not implement "${e}"`)}function Wr(e,t){if(!(e instanceof Uint8Array))throw new TypeError(`The "${t}" argument must be an instance of Buffer or Uint8Array`)}function X(e,t,r=hc+1){if(e<0||e>r){let n=new RangeError(`The value of "${t}" is out of range. It must be >= 0 && <= ${r}. Received ${e}`);throw n.code="ERR_OUT_OF_RANGE",n}}function Y(e,t){if(typeof e!="number"){let r=new TypeError(`The "${t}" argument must be of type number. Received type ${typeof e}.`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function pe(e,t){if(!Number.isInteger(e)||Number.isNaN(e)){let r=new RangeError(`The value of "${t}" is out of range. It must be an integer. Received ${e}`);throw r.code="ERR_OUT_OF_RANGE",r}}function mc(e,t,r,n){if(e<r||e>n){let i=new RangeError(`The value of "${t}" is out of range. It must be >= ${r} and <= ${n}. Received ${e}`);throw i.code="ERR_OUT_OF_RANGE",i}}function Vo(e,t){if(typeof e!="string"){let r=new TypeError(`The "${t}" argument must be of type string. Received type ${typeof e}`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function wc(e,t="utf8"){return h.from(e,t)}var h,dc,fc,gc,yc,hc,y,ai,u=ge(()=>{"use strict";h=class e extends Uint8Array{_isBuffer=!0;get offset(){return this.byteOffset}static alloc(t,r=0,n="utf8"){return Vo(n,"encoding"),e.allocUnsafe(t).fill(r,n)}static allocUnsafe(t){return e.from(t)}static allocUnsafeSlow(t){return e.from(t)}static isBuffer(t){return t&&!!t._isBuffer}static byteLength(t,r="utf8"){if(typeof t=="string")return si(t,r).byteLength;if(t&&t.byteLength)return t.byteLength;let n=new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');throw n.code="ERR_INVALID_ARG_TYPE",n}static isEncoding(t){return yc.includes(t)}static compare(t,r){Wr(t,"buff1"),Wr(r,"buff2");for(let n=0;n<t.length;n++){if(t[n]<r[n])return-1;if(t[n]>r[n])return 1}return t.length===r.length?0:t.length>r.length?1:-1}static from(t,r="utf8"){if(t&&typeof t=="object"&&t.type==="Buffer")return new e(t.data);if(typeof t=="number")return new e(new Uint8Array(t));if(typeof t=="string")return si(t,r);if(ArrayBuffer.isView(t)){let{byteOffset:n,byteLength:i,buffer:o}=t;return"map"in t&&typeof t.map=="function"?new e(t.map(s=>s%256),n,i):new e(o,n,i)}if(t&&typeof t=="object"&&("length"in t||"byteLength"in t||"buffer"in t))return new e(t);throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}static concat(t,r){if(t.length===0)return e.alloc(0);let n=[].concat(...t.map(o=>[...o])),i=e.alloc(r!==void 0?r:n.length);return i.set(r!==void 0?n.slice(0,r):n),i}slice(t=0,r=this.length){return this.subarray(t,r)}subarray(t=0,r=this.length){return Object.setPrototypeOf(super.subarray(t,r),e.prototype)}reverse(){return super.reverse(),this}readIntBE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return n.getUint8(0)&128&&(i-=Math.pow(256,r)),i}readIntLE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return n.getUint8(r-1)&128&&(i-=Math.pow(256,r)),i}readUIntBE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return i}readUintBE(t,r){return this.readUIntBE(t,r)}readUIntLE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return i}readUintLE(t,r){return this.readUIntLE(t,r)}writeIntBE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntBE(t,r,n)}writeIntLE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntLE(t,r,n)}writeUIntBE(t,r,n){Y(r,"offset"),pe(r,"offset"),X(r,"offset",this.length-1),Y(n,"byteLength"),pe(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=n-1;o>=0;o--)i.setUint8(o,t&255),t=t/256;return r+n}writeUintBE(t,r,n){return this.writeUIntBE(t,r,n)}writeUIntLE(t,r,n){Y(r,"offset"),pe(r,"offset"),X(r,"offset",this.length-1),Y(n,"byteLength"),pe(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=0;o<n;o++)i.setUint8(o,t&255),t=t/256;return r+n}writeUintLE(t,r,n){return this.writeUIntLE(t,r,n)}toJSON(){return{type:"Buffer",data:Array.from(this)}}swap16(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=2)t.setUint16(r,t.getUint16(r,!0),!1);return this}swap32(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=4)t.setUint32(r,t.getUint32(r,!0),!1);return this}swap64(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=8)t.setBigUint64(r,t.getBigUint64(r,!0),!1);return this}compare(t,r=0,n=t.length,i=0,o=this.length){return Wr(t,"target"),Y(r,"targetStart"),Y(n,"targetEnd"),Y(i,"sourceStart"),Y(o,"sourceEnd"),X(r,"targetStart"),X(n,"targetEnd",t.length),X(i,"sourceStart"),X(o,"sourceEnd",this.length),e.compare(this.slice(i,o),t.slice(r,n))}equals(t){return Wr(t,"otherBuffer"),this.length===t.length&&this.every((r,n)=>r===t[n])}copy(t,r=0,n=0,i=this.length){X(r,"targetStart"),X(n,"sourceStart",this.length),X(i,"sourceEnd"),r>>>=0,n>>>=0,i>>>=0;let o=0;for(;n<i&&!(this[n]===void 0||t[r]===void 0);)t[r]=this[n],o++,n++,r++;return o}write(t,r,n,i="utf8"){let o=typeof r=="string"?0:r??0,s=typeof n=="string"?this.length-o:n??this.length-o;return i=typeof r=="string"?r:typeof n=="string"?n:i,Y(o,"offset"),Y(s,"length"),X(o,"offset",this.length),X(s,"length",this.length),(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")&&(s=s-s%2),si(t,i).copy(this,o,0,s)}fill(t=0,r=0,n=this.length,i="utf-8"){let o=typeof r=="string"?0:r,s=typeof n=="string"?this.length:n;if(i=typeof r=="string"?r:typeof n=="string"?n:i,t=e.from(typeof t=="number"?[t]:t??[],i),Vo(i,"encoding"),X(o,"offset",this.length),X(s,"end",this.length),t.length!==0)for(let a=o;a<s;a+=t.length)super.set(t.slice(0,t.length+a>=this.length?this.length-a:t.length),a);return this}includes(t,r=null,n="utf-8"){return this.indexOf(t,r,n)!==-1}lastIndexOf(t,r=null,n="utf-8"){return this.indexOf(t,r,n,!0)}indexOf(t,r=null,n="utf-8",i=!1){let o=i?this.findLastIndex.bind(this):this.findIndex.bind(this);n=typeof r=="string"?r:n;let s=e.from(typeof t=="number"?[t]:t,n),a=typeof r=="string"?0:r;return a=typeof r=="number"?a:null,a=Number.isNaN(a)?null:a,a??=i?this.length:0,a=a<0?this.length+a:a,s.length===0&&i===!1?a>=this.length?this.length:a:s.length===0&&i===!0?(a>=this.length?this.length:a)||this.length:o((f,w)=>(i?w<=a:w>=a)&&this[w]===s[0]&&s.every((C,I)=>this[w+I]===C))}toString(t="utf8",r=0,n=this.length){if(r=r<0?0:r,t=t.toString().toLowerCase(),n<=0)return"";if(t==="utf8"||t==="utf-8")return gc.decode(this.slice(r,n));if(t==="base64"||t==="base64url"){let i=btoa(this.reduce((o,s)=>o+ai(s),""));return t==="base64url"?i.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):i}if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return this.slice(r,n).reduce((i,o)=>i+ai(o&(t==="ascii"?127:255)),"");if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let i=new DataView(this.buffer.slice(r,n));return Array.from({length:i.byteLength/2},(o,s)=>s*2+1<i.byteLength?ai(i.getUint16(s*2,!0)):"").join("")}if(t==="hex")return this.slice(r,n).reduce((i,o)=>i+o.toString(16).padStart(2,"0"),"");$o(`encoding "${t}"`)}toLocaleString(){return this.toString()}inspect(){return`<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`}};dc={int8:[-128,127],int16:[-32768,32767],int32:[-2147483648,2147483647],uint8:[0,255],uint16:[0,65535],uint32:[0,4294967295],float32:[-1/0,1/0],float64:[-1/0,1/0],bigint64:[-0x8000000000000000n,0x7fffffffffffffffn],biguint64:[0n,0xffffffffffffffffn]},fc=new TextEncoder,gc=new TextDecoder,yc=["utf8","utf-8","hex","base64","ascii","binary","base64url","ucs2","ucs-2","utf16le","utf-16le","latin1","latin-1"],hc=4294967295;pc(h.prototype);y=new Proxy(wc,{construct(e,[t,r]){return h.from(t,r)},get(e,t){return h[t]}}),ai=String.fromCodePoint});var g,x,c=ge(()=>{"use strict";g={nextTick:(e,...t)=>{setTimeout(()=>{e(...t)},0)},env:{},version:"",cwd:()=>"/",stderr:{},argv:["/bin/node"],pid:1e4},{cwd:x}=g});var b,p=ge(()=>{"use strict";b=globalThis.performance??(()=>{let e=Date.now();return{now:()=>Date.now()-e}})()});var E,m=ge(()=>{"use strict";E=()=>{};E.prototype=E});var d=ge(()=>{"use strict"});function Qo(e,t){var r,n,i,o,s,a,f,w,A=e.constructor,C=A.precision;if(!e.s||!t.s)return t.s||(t=new A(e)),J?q(t,C):t;if(f=e.d,w=t.d,s=e.e,i=t.e,f=f.slice(),o=s-i,o){for(o<0?(n=f,o=-o,a=w.length):(n=w,i=s,a=f.length),s=Math.ceil(C/H),a=s>a?s+1:a+1,o>a&&(o=a,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for(a=f.length,o=w.length,a-o<0&&(o=a,n=w,w=f,f=n),r=0;o;)r=(f[--o]=f[o]+w[o]+r)/te|0,f[o]%=te;for(r&&(f.unshift(r),++i),a=f.length;f[--a]==0;)f.pop();return t.d=f,t.e=i,J?q(t,C):t}function Re(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ze+e)}function Ce(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=H-n.length,r&&(o+=$e(r)),o+=n;s=e[t],n=s+"",r=H-n.length,r&&(o+=$e(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function Ho(e,t){var r,n,i,o,s,a,f=0,w=0,A=e.constructor,C=A.precision;if(Z(e)>16)throw Error(ui+Z(e));if(!e.s)return new A(ye);for(t==null?(J=!1,a=C):a=t,s=new A(.03125);e.abs().gte(.1);)e=e.times(s),w+=5;for(n=Math.log(Ke(2,w))/Math.LN10*2+5|0,a+=n,r=i=o=new A(ye),A.precision=a;;){if(i=q(i.times(e),a),r=r.times(++f),s=o.plus(Me(i,r,a)),Ce(s.d).slice(0,a)===Ce(o.d).slice(0,a)){for(;w--;)o=q(o.times(o),a);return A.precision=C,t==null?(J=!0,q(o,C)):o}o=s}}function Z(e){for(var t=e.e*H,r=e.d[0];r>=10;r/=10)t++;return t}function li(e,t,r){if(t>e.LN10.sd())throw J=!0,r&&(e.precision=r),Error(Ee+"LN10 precision limit exceeded");return q(new e(e.LN10),t)}function $e(e){for(var t="";e--;)t+="0";return t}function Gt(e,t){var r,n,i,o,s,a,f,w,A,C=1,I=10,R=e,L=R.d,k=R.constructor,M=k.precision;if(R.s<1)throw Error(Ee+(R.s?"NaN":"-Infinity"));if(R.eq(ye))return new k(0);if(t==null?(J=!1,w=M):w=t,R.eq(10))return t==null&&(J=!0),li(k,w);if(w+=I,k.precision=w,r=Ce(L),n=r.charAt(0),o=Z(R),Math.abs(o)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)R=R.times(e),r=Ce(R.d),n=r.charAt(0),C++;o=Z(R),n>1?(R=new k("0."+r),o++):R=new k(n+"."+r.slice(1))}else return f=li(k,w+2,M).times(o+""),R=Gt(new k(n+"."+r.slice(1)),w-I).plus(f),k.precision=M,t==null?(J=!0,q(R,M)):R;for(a=s=R=Me(R.minus(ye),R.plus(ye),w),A=q(R.times(R),w),i=3;;){if(s=q(s.times(A),w),f=a.plus(Me(s,new k(i),w)),Ce(f.d).slice(0,w)===Ce(a.d).slice(0,w))return a=a.times(2),o!==0&&(a=a.plus(li(k,w+2,M).times(o+""))),a=Me(a,new k(C),w),k.precision=M,t==null?(J=!0,q(a,M)):a;a=f,i+=2}}function qo(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=mt(r/H),e.d=[],n=(r+1)%H,r<0&&(n+=H),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=H;n<i;)e.d.push(+t.slice(n,n+=H));t=t.slice(n),n=H-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),J&&(e.e>Kr||e.e<-Kr))throw Error(ui+r)}else e.s=0,e.e=0,e.d=[0];return e}function q(e,t,r){var n,i,o,s,a,f,w,A,C=e.d;for(s=1,o=C[0];o>=10;o/=10)s++;if(n=t-s,n<0)n+=H,i=t,w=C[A=0];else{if(A=Math.ceil((n+1)/H),o=C.length,A>=o)return e;for(w=o=C[A],s=1;o>=10;o/=10)s++;n%=H,i=n-H+s}if(r!==void 0&&(o=Ke(10,s-i-1),a=w/o%10|0,f=t<0||C[A+1]!==void 0||w%o,f=r<4?(a||f)&&(r==0||r==(e.s<0?3:2)):a>5||a==5&&(r==4||f||r==6&&(n>0?i>0?w/Ke(10,s-i):0:C[A-1])%10&1||r==(e.s<0?8:7))),t<1||!C[0])return f?(o=Z(e),C.length=1,t=t-o-1,C[0]=Ke(10,(H-t%H)%H),e.e=mt(-t/H)||0):(C.length=1,C[0]=e.e=e.s=0),e;if(n==0?(C.length=A,o=1,A--):(C.length=A+1,o=Ke(10,H-n),C[A]=i>0?(w/Ke(10,s-i)%Ke(10,i)|0)*o:0),f)for(;;)if(A==0){(C[0]+=o)==te&&(C[0]=1,++e.e);break}else{if(C[A]+=o,C[A]!=te)break;C[A--]=0,o=1}for(n=C.length;C[--n]===0;)C.pop();if(J&&(e.e>Kr||e.e<-Kr))throw Error(ui+Z(e));return e}function Go(e,t){var r,n,i,o,s,a,f,w,A,C,I=e.constructor,R=I.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new I(e),J?q(t,R):t;if(f=e.d,C=t.d,n=t.e,w=e.e,f=f.slice(),s=w-n,s){for(A=s<0,A?(r=f,s=-s,a=C.length):(r=C,n=w,a=f.length),i=Math.max(Math.ceil(R/H),a)+2,s>i&&(s=i,r.length=1),r.reverse(),i=s;i--;)r.push(0);r.reverse()}else{for(i=f.length,a=C.length,A=i<a,A&&(a=i),i=0;i<a;i++)if(f[i]!=C[i]){A=f[i]<C[i];break}s=0}for(A&&(r=f,f=C,C=r,t.s=-t.s),a=f.length,i=C.length-a;i>0;--i)f[a++]=0;for(i=C.length;i>s;){if(f[--i]<C[i]){for(o=i;o&&f[--o]===0;)f[o]=te-1;--f[o],f[i]+=te}f[i]-=C[i]}for(;f[--a]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(t.d=f,t.e=n,J?q(t,R):t):new I(0)}function Ye(e,t,r){var n,i=Z(e),o=Ce(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+$e(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+$e(-i-1)+o,r&&(n=r-s)>0&&(o+=$e(n))):i>=s?(o+=$e(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+$e(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=$e(n))),e.s<0?"-"+o:o}function Bo(e,t){if(e.length>t)return e.length=t,!0}function Jo(e){var t,r,n;function i(o){var s=this;if(!(s instanceof i))return new i(o);if(s.constructor=i,o instanceof i){s.s=o.s,s.e=o.e,s.d=(o=o.d)?o.slice():o;return}if(typeof o=="number"){if(o*0!==0)throw Error(ze+o);if(o>0)s.s=1;else if(o<0)o=-o,s.s=-1;else{s.s=0,s.e=0,s.d=[0];return}if(o===~~o&&o<1e7){s.e=0,s.d=[o];return}return qo(s,o.toString())}else if(typeof o!="string")throw Error(ze+o);if(o.charCodeAt(0)===45?(o=o.slice(1),s.s=-1):s.s=1,Ec.test(o))qo(s,o);else throw Error(ze+o)}if(i.prototype=S,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Jo,i.config=i.set=xc,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function xc(e){if(!e||typeof e!="object")throw Error(Ee+"Object expected");var t,r,n,i=["precision",1,pt,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(mt(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(ze+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(ze+r+": "+n);return this}var pt,bc,ci,J,Ee,ze,ui,mt,Ke,Ec,ye,te,H,jo,Kr,S,Me,ci,zr,Wo=ge(()=>{"use strict";u();c();p();m();d();l();pt=1e9,bc={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},J=!0,Ee="[DecimalError] ",ze=Ee+"Invalid argument: ",ui=Ee+"Exponent out of range: ",mt=Math.floor,Ke=Math.pow,Ec=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,te=1e7,H=7,jo=9007199254740991,Kr=mt(jo/H),S={};S.absoluteValue=S.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};S.comparedTo=S.cmp=function(e){var t,r,n,i,o=this;if(e=new o.constructor(e),o.s!==e.s)return o.s||-e.s;if(o.e!==e.e)return o.e>e.e^o.s<0?1:-1;for(n=o.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(o.d[t]!==e.d[t])return o.d[t]>e.d[t]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1};S.decimalPlaces=S.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*H;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};S.dividedBy=S.div=function(e){return Me(this,new this.constructor(e))};S.dividedToIntegerBy=S.idiv=function(e){var t=this,r=t.constructor;return q(Me(t,new r(e),0,1),r.precision)};S.equals=S.eq=function(e){return!this.cmp(e)};S.exponent=function(){return Z(this)};S.greaterThan=S.gt=function(e){return this.cmp(e)>0};S.greaterThanOrEqualTo=S.gte=function(e){return this.cmp(e)>=0};S.isInteger=S.isint=function(){return this.e>this.d.length-2};S.isNegative=S.isneg=function(){return this.s<0};S.isPositive=S.ispos=function(){return this.s>0};S.isZero=function(){return this.s===0};S.lessThan=S.lt=function(e){return this.cmp(e)<0};S.lessThanOrEqualTo=S.lte=function(e){return this.cmp(e)<1};S.logarithm=S.log=function(e){var t,r=this,n=r.constructor,i=n.precision,o=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(ye))throw Error(Ee+"NaN");if(r.s<1)throw Error(Ee+(r.s?"NaN":"-Infinity"));return r.eq(ye)?new n(0):(J=!1,t=Me(Gt(r,o),Gt(e,o),o),J=!0,q(t,i))};S.minus=S.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Go(t,e):Qo(t,(e.s=-e.s,e))};S.modulo=S.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ee+"NaN");return r.s?(J=!1,t=Me(r,e,0,1).times(e),J=!0,r.minus(t)):q(new n(r),i)};S.naturalExponential=S.exp=function(){return Ho(this)};S.naturalLogarithm=S.ln=function(){return Gt(this)};S.negated=S.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};S.plus=S.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Qo(t,e):Go(t,(e.s=-e.s,e))};S.precision=S.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(ze+e);if(t=Z(i)+1,n=i.d.length-1,r=n*H+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};S.squareRoot=S.sqrt=function(){var e,t,r,n,i,o,s,a=this,f=a.constructor;if(a.s<1){if(!a.s)return new f(0);throw Error(Ee+"NaN")}for(e=Z(a),J=!1,i=Math.sqrt(+a),i==0||i==1/0?(t=Ce(a.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=mt((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new f(t)):n=new f(i.toString()),r=f.precision,i=s=r+3;;)if(o=n,n=o.plus(Me(a,o,s+2)).times(.5),Ce(o.d).slice(0,s)===(t=Ce(n.d)).slice(0,s)){if(t=t.slice(s-3,s+1),i==s&&t=="4999"){if(q(o,r+1,0),o.times(o).eq(a)){n=o;break}}else if(t!="9999")break;s+=4}return J=!0,q(n,r)};S.times=S.mul=function(e){var t,r,n,i,o,s,a,f,w,A=this,C=A.constructor,I=A.d,R=(e=new C(e)).d;if(!A.s||!e.s)return new C(0);for(e.s*=A.s,r=A.e+e.e,f=I.length,w=R.length,f<w&&(o=I,I=R,R=o,s=f,f=w,w=s),o=[],s=f+w,n=s;n--;)o.push(0);for(n=w;--n>=0;){for(t=0,i=f+n;i>n;)a=o[i]+R[n]*I[i-n-1]+t,o[i--]=a%te|0,t=a/te|0;o[i]=(o[i]+t)%te|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,J?q(e,C.precision):e};S.toDecimalPlaces=S.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Re(e,0,pt),t===void 0?t=n.rounding:Re(t,0,8),q(r,e+Z(r)+1,t))};S.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Ye(n,!0):(Re(e,0,pt),t===void 0?t=i.rounding:Re(t,0,8),n=q(new i(n),e+1,t),r=Ye(n,!0,e+1)),r};S.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?Ye(i):(Re(e,0,pt),t===void 0?t=o.rounding:Re(t,0,8),n=q(new o(i),e+Z(i)+1,t),r=Ye(n.abs(),!1,e+Z(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};S.toInteger=S.toint=function(){var e=this,t=e.constructor;return q(new t(e),Z(e)+1,t.rounding)};S.toNumber=function(){return+this};S.toPower=S.pow=function(e){var t,r,n,i,o,s,a=this,f=a.constructor,w=12,A=+(e=new f(e));if(!e.s)return new f(ye);if(a=new f(a),!a.s){if(e.s<1)throw Error(Ee+"Infinity");return a}if(a.eq(ye))return a;if(n=f.precision,e.eq(ye))return q(a,n);if(t=e.e,r=e.d.length-1,s=t>=r,o=a.s,s){if((r=A<0?-A:A)<=jo){for(i=new f(ye),t=Math.ceil(n/H+4),J=!1;r%2&&(i=i.times(a),Bo(i.d,t)),r=mt(r/2),r!==0;)a=a.times(a),Bo(a.d,t);return J=!0,e.s<0?new f(ye).div(i):q(i,n)}}else if(o<0)throw Error(Ee+"NaN");return o=o<0&&e.d[Math.max(t,r)]&1?-1:1,a.s=1,J=!1,i=e.times(Gt(a,n+w)),J=!0,i=Ho(i),i.s=o,i};S.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?(r=Z(i),n=Ye(i,r<=o.toExpNeg||r>=o.toExpPos)):(Re(e,1,pt),t===void 0?t=o.rounding:Re(t,0,8),i=q(new o(i),e,t),r=Z(i),n=Ye(i,e<=r||r<=o.toExpNeg,e)),n};S.toSignificantDigits=S.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Re(e,1,pt),t===void 0?t=n.rounding:Re(t,0,8)),q(new n(r),e,t)};S.toString=S.valueOf=S.val=S.toJSON=S[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=Z(e),r=e.constructor;return Ye(e,t<=r.toExpNeg||t>=r.toExpPos)};Me=function(){function e(n,i){var o,s=0,a=n.length;for(n=n.slice();a--;)o=n[a]*i+s,n[a]=o%te|0,s=o/te|0;return s&&n.unshift(s),n}function t(n,i,o,s){var a,f;if(o!=s)f=o>s?1:-1;else for(a=f=0;a<o;a++)if(n[a]!=i[a]){f=n[a]>i[a]?1:-1;break}return f}function r(n,i,o){for(var s=0;o--;)n[o]-=s,s=n[o]<i[o]?1:0,n[o]=s*te+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s){var a,f,w,A,C,I,R,L,k,M,_e,ue,B,ce,We,oi,xe,Hr,Gr=n.constructor,ic=n.s==i.s?1:-1,ve=n.d,z=i.d;if(!n.s)return new Gr(n);if(!i.s)throw Error(Ee+"Division by zero");for(f=n.e-i.e,xe=z.length,We=ve.length,R=new Gr(ic),L=R.d=[],w=0;z[w]==(ve[w]||0);)++w;if(z[w]>(ve[w]||0)&&--f,o==null?ue=o=Gr.precision:s?ue=o+(Z(n)-Z(i))+1:ue=o,ue<0)return new Gr(0);if(ue=ue/H+2|0,w=0,xe==1)for(A=0,z=z[0],ue++;(w<We||A)&&ue--;w++)B=A*te+(ve[w]||0),L[w]=B/z|0,A=B%z|0;else{for(A=te/(z[0]+1)|0,A>1&&(z=e(z,A),ve=e(ve,A),xe=z.length,We=ve.length),ce=xe,k=ve.slice(0,xe),M=k.length;M<xe;)k[M++]=0;Hr=z.slice(),Hr.unshift(0),oi=z[0],z[1]>=te/2&&++oi;do A=0,a=t(z,k,xe,M),a<0?(_e=k[0],xe!=M&&(_e=_e*te+(k[1]||0)),A=_e/oi|0,A>1?(A>=te&&(A=te-1),C=e(z,A),I=C.length,M=k.length,a=t(C,k,I,M),a==1&&(A--,r(C,xe<I?Hr:z,I))):(A==0&&(a=A=1),C=z.slice()),I=C.length,I<M&&C.unshift(0),r(k,C,M),a==-1&&(M=k.length,a=t(z,k,xe,M),a<1&&(A++,r(k,xe<M?Hr:z,M))),M=k.length):a===0&&(A++,k=[0]),L[w++]=A,a&&k[0]?k[M++]=ve[ce]||0:(k=[ve[ce]],M=1);while((ce++<We||k[0]!==void 0)&&ue--)}return L[0]||L.shift(),R.e=f,q(R,s?o+Z(R)+1:o)}}();ci=Jo(bc);ye=new ci(1);zr=ci});var v,me,l=ge(()=>{"use strict";Wo();v=class extends zr{static isDecimal(t){return t instanceof zr}static random(t=20){{let n=globalThis.crypto.getRandomValues(new Uint8Array(t)).reduce((i,o)=>i+o,"");return new zr(`0.${n.slice(0,t)}`)}}},me=v});function Rc(){return!1}function di(){return{dev:0,ino:0,mode:0,nlink:0,uid:0,gid:0,rdev:0,size:0,blksize:0,blocks:0,atimeMs:0,mtimeMs:0,ctimeMs:0,birthtimeMs:0,atime:new Date,mtime:new Date,ctime:new Date,birthtime:new Date}}function Ic(){return di()}function Sc(){return[]}function kc(e){e(null,[])}function Oc(){return""}function Dc(){return""}function _c(){}function Mc(){}function Nc(){}function Lc(){}function Uc(){}function Fc(){}function Vc(){}function $c(){}function qc(){return{close:()=>{},on:()=>{},removeAllListeners:()=>{}}}function Bc(e,t){t(null,di())}var jc,Qc,ds,fs=ge(()=>{"use strict";u();c();p();m();d();l();jc={},Qc={existsSync:Rc,lstatSync:di,stat:Bc,statSync:Ic,readdirSync:Sc,readdir:kc,readlinkSync:Oc,realpathSync:Dc,chmodSync:_c,renameSync:Mc,mkdirSync:Nc,rmdirSync:Lc,rmSync:Uc,unlinkSync:Fc,watchFile:Vc,unwatchFile:$c,watch:qc,promises:jc},ds=Qc});var gs=ie(()=>{"use strict";u();c();p();m();d();l()});function Hc(...e){return e.join("/")}function Gc(...e){return e.join("/")}function Jc(e){let t=ys(e),r=hs(e),[n,i]=t.split(".");return{root:"/",dir:r,base:t,ext:i,name:n}}function ys(e){let t=e.split("/");return t[t.length-1]}function hs(e){return e.split("/").slice(0,-1).join("/")}function Kc(e){let t=e.split("/").filter(i=>i!==""&&i!=="."),r=[];for(let i of t)i===".."?r.pop():r.push(i);let n=r.join("/");return e.startsWith("/")?"/"+n:n}var ws,Wc,zc,Yc,en,bs=ge(()=>{"use strict";u();c();p();m();d();l();ws="/",Wc=":";zc={sep:ws},Yc={basename:ys,delimiter:Wc,dirname:hs,join:Gc,normalize:Kc,parse:Jc,posix:zc,resolve:Hc,sep:ws},en=Yc});var Es=ie((Xy,Zc)=>{Zc.exports={name:"@prisma/internals",version:"6.14.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",empathic:"2.0.0",esbuild:"0.25.5","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49","@prisma/schema-engine-wasm":"6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var gi={};Ht(gi,{Hash:()=>Kt,createHash:()=>xs,default:()=>gt,randomFillSync:()=>nn,randomUUID:()=>rn,webcrypto:()=>zt});function rn(){return globalThis.crypto.randomUUID()}function nn(e,t,r){return t!==void 0&&(r!==void 0?e=e.subarray(t,t+r):e=e.subarray(t)),globalThis.crypto.getRandomValues(e)}function xs(e){return new Kt(e)}var zt,Kt,gt,Ze=ge(()=>{"use strict";u();c();p();m();d();l();zt=globalThis.crypto;Kt=class{#e=[];#t;constructor(t){this.#t=t}update(t){this.#e.push(t)}async digest(){let t=new Uint8Array(this.#e.reduce((i,o)=>i+o.length,0)),r=0;for(let i of this.#e)t.set(i,r),r+=i.length;let n=await globalThis.crypto.subtle.digest(this.#t,t);return new Uint8Array(n)}},gt={webcrypto:zt,randomUUID:rn,randomFillSync:nn,createHash:xs,Hash:Kt}});var yi=ie((Hh,rp)=>{rp.exports={name:"@prisma/engines-version",version:"6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"717184b7b35ea05dfa71a3236b7af656013e1e49"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var Ps=ie(on=>{"use strict";u();c();p();m();d();l();Object.defineProperty(on,"__esModule",{value:!0});on.enginesVersion=void 0;on.enginesVersion=yi().prisma.enginesVersion});var As=ie((sw,vs)=>{"use strict";u();c();p();m();d();l();vs.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var Is=ie((Ew,Rs)=>{"use strict";u();c();p();m();d();l();Rs.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var bi=ie((Rw,Ss)=>{"use strict";u();c();p();m();d();l();var ap=Is();Ss.exports=e=>typeof e=="string"?e.replace(ap(),""):e});var ks=ie((qw,ln)=>{"use strict";u();c();p();m();d();l();ln.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};ln.exports.default=ln.exports});var Ti=ie((_P,Ms)=>{"use strict";u();c();p();m();d();l();Ms.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,f,w,A,C,I,R,L,k,M,_e,ue,B,ce=[];for(f=0;f<i;f++)ce.push(f+1),ce.push(t.charCodeAt(s+f));for(var We=ce.length-1;a<o-3;)for(M=r.charCodeAt(s+(w=a)),_e=r.charCodeAt(s+(A=a+1)),ue=r.charCodeAt(s+(C=a+2)),B=r.charCodeAt(s+(I=a+3)),R=a+=4,f=0;f<We;f+=2)L=ce[f],k=ce[f+1],w=e(L,w,A,M,k),A=e(w,A,C,_e,k),C=e(A,C,I,ue,k),R=e(C,I,R,B,k),ce[f]=R,I=C,C=A,A=w,w=L;for(;a<o;)for(M=r.charCodeAt(s+(w=a)),R=++a,f=0;f<We;f+=2)L=ce[f],ce[f]=R=e(L,w,R,M,ce[f+1]),w=L;return R}}()});var Vs=ge(()=>{"use strict";u();c();p();m();d();l()});var $s=ge(()=>{"use strict";u();c();p();m();d();l()});var Rn,aa=ge(()=>{"use strict";u();c();p();m();d();l();Rn=class{events={};on(t,r){return this.events[t]||(this.events[t]=[]),this.events[t].push(r),this}emit(t,...r){return this.events[t]?(this.events[t].forEach(n=>{n(...r)}),!0):!1}}});var Ki=ie(rt=>{"use strict";u();c();p();m();d();l();Object.defineProperty(rt,"__esModule",{value:!0});rt.anumber=Wi;rt.abytes=il;rt.ahash=Wm;rt.aexists=Km;rt.aoutput=zm;function Wi(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function Jm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function il(e,...t){if(!Jm(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Wm(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Wi(e.outputLen),Wi(e.blockLen)}function Km(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function zm(e,t){il(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var Cl=ie(_=>{"use strict";u();c();p();m();d();l();Object.defineProperty(_,"__esModule",{value:!0});_.add5L=_.add5H=_.add4H=_.add4L=_.add3H=_.add3L=_.rotlBL=_.rotlBH=_.rotlSL=_.rotlSH=_.rotr32L=_.rotr32H=_.rotrBL=_.rotrBH=_.rotrSL=_.rotrSH=_.shrSL=_.shrSH=_.toBig=void 0;_.fromBig=Yi;_.split=ol;_.add=bl;var Nn=BigInt(2**32-1),zi=BigInt(32);function Yi(e,t=!1){return t?{h:Number(e&Nn),l:Number(e>>zi&Nn)}:{h:Number(e>>zi&Nn)|0,l:Number(e&Nn)|0}}function ol(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=Yi(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var sl=(e,t)=>BigInt(e>>>0)<<zi|BigInt(t>>>0);_.toBig=sl;var al=(e,t,r)=>e>>>r;_.shrSH=al;var ll=(e,t,r)=>e<<32-r|t>>>r;_.shrSL=ll;var ul=(e,t,r)=>e>>>r|t<<32-r;_.rotrSH=ul;var cl=(e,t,r)=>e<<32-r|t>>>r;_.rotrSL=cl;var pl=(e,t,r)=>e<<64-r|t>>>r-32;_.rotrBH=pl;var ml=(e,t,r)=>e>>>r-32|t<<64-r;_.rotrBL=ml;var dl=(e,t)=>t;_.rotr32H=dl;var fl=(e,t)=>e;_.rotr32L=fl;var gl=(e,t,r)=>e<<r|t>>>32-r;_.rotlSH=gl;var yl=(e,t,r)=>t<<r|e>>>32-r;_.rotlSL=yl;var hl=(e,t,r)=>t<<r-32|e>>>64-r;_.rotlBH=hl;var wl=(e,t,r)=>e<<r-32|t>>>64-r;_.rotlBL=wl;function bl(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var El=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);_.add3L=El;var xl=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;_.add3H=xl;var Pl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);_.add4L=Pl;var Tl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;_.add4H=Tl;var vl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);_.add5L=vl;var Al=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;_.add5H=Al;var Ym={fromBig:Yi,split:ol,toBig:sl,shrSH:al,shrSL:ll,rotrSH:ul,rotrSL:cl,rotrBH:pl,rotrBL:ml,rotr32H:dl,rotr32L:fl,rotlSH:gl,rotlSL:yl,rotlBH:hl,rotlBL:wl,add:bl,add3L:El,add3H:xl,add4L:Pl,add4H:Tl,add5H:Al,add5L:vl};_.default=Ym});var Rl=ie(Ln=>{"use strict";u();c();p();m();d();l();Object.defineProperty(Ln,"__esModule",{value:!0});Ln.crypto=void 0;var Qe=(Ze(),cc(gi));Ln.crypto=Qe&&typeof Qe=="object"&&"webcrypto"in Qe?Qe.webcrypto:Qe&&typeof Qe=="object"&&"randomBytes"in Qe?Qe:void 0});var kl=ie(U=>{"use strict";u();c();p();m();d();l();Object.defineProperty(U,"__esModule",{value:!0});U.Hash=U.nextTick=U.byteSwapIfBE=U.isLE=void 0;U.isBytes=Zm;U.u8=Xm;U.u32=ed;U.createView=td;U.rotr=rd;U.rotl=nd;U.byteSwap=eo;U.byteSwap32=id;U.bytesToHex=sd;U.hexToBytes=ad;U.asyncLoop=ud;U.utf8ToBytes=Sl;U.toBytes=Un;U.concatBytes=cd;U.checkOpts=pd;U.wrapConstructor=md;U.wrapConstructorWithOpts=dd;U.wrapXOFConstructorWithOpts=fd;U.randomBytes=gd;var Ot=Rl(),Xi=Ki();function Zm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Xm(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function ed(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function td(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function rd(e,t){return e<<32-t|e>>>t}function nd(e,t){return e<<t|e>>>32-t>>>0}U.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function eo(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}U.byteSwapIfBE=U.isLE?e=>e:e=>eo(e);function id(e){for(let t=0;t<e.length;t++)e[t]=eo(e[t])}var od=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function sd(e){(0,Xi.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=od[e[r]];return t}var Ue={_0:48,_9:57,A:65,F:70,a:97,f:102};function Il(e){if(e>=Ue._0&&e<=Ue._9)return e-Ue._0;if(e>=Ue.A&&e<=Ue.F)return e-(Ue.A-10);if(e>=Ue.a&&e<=Ue.f)return e-(Ue.a-10)}function ad(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=Il(e.charCodeAt(o)),a=Il(e.charCodeAt(o+1));if(s===void 0||a===void 0){let f=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+f+'" at index '+o)}n[i]=s*16+a}return n}var ld=async()=>{};U.nextTick=ld;async function ud(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,U.nextTick)(),n+=o)}}function Sl(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function Un(e){return typeof e=="string"&&(e=Sl(e)),(0,Xi.abytes)(e),e}function cd(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,Xi.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var Zi=class{clone(){return this._cloneInto()}};U.Hash=Zi;function pd(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function md(e){let t=n=>e().update(Un(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function dd(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function fd(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function gd(e=32){if(Ot.crypto&&typeof Ot.crypto.getRandomValues=="function")return Ot.crypto.getRandomValues(new Uint8Array(e));if(Ot.crypto&&typeof Ot.crypto.randomBytes=="function")return Ot.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var Fl=ie(G=>{"use strict";u();c();p();m();d();l();Object.defineProperty(G,"__esModule",{value:!0});G.shake256=G.shake128=G.keccak_512=G.keccak_384=G.keccak_256=G.keccak_224=G.sha3_512=G.sha3_384=G.sha3_256=G.sha3_224=G.Keccak=void 0;G.keccakP=Ll;var Dt=Ki(),br=Cl(),Fe=kl(),_l=[],Ml=[],Nl=[],yd=BigInt(0),wr=BigInt(1),hd=BigInt(2),wd=BigInt(7),bd=BigInt(256),Ed=BigInt(113);for(let e=0,t=wr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],_l.push(2*(5*n+r)),Ml.push((e+1)*(e+2)/2%64);let i=yd;for(let o=0;o<7;o++)t=(t<<wr^(t>>wd)*Ed)%bd,t&hd&&(i^=wr<<(wr<<BigInt(o))-wr);Nl.push(i)}var[xd,Pd]=(0,br.split)(Nl,!0),Ol=(e,t,r)=>r>32?(0,br.rotlBH)(e,t,r):(0,br.rotlSH)(e,t,r),Dl=(e,t,r)=>r>32?(0,br.rotlBL)(e,t,r):(0,br.rotlSL)(e,t,r);function Ll(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,f=(s+2)%10,w=r[f],A=r[f+1],C=Ol(w,A,1)^r[a],I=Dl(w,A,1)^r[a+1];for(let R=0;R<50;R+=10)e[s+R]^=C,e[s+R+1]^=I}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=Ml[s],f=Ol(i,o,a),w=Dl(i,o,a),A=_l[s];i=e[A],o=e[A+1],e[A]=f,e[A+1]=w}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=xd[n],e[1]^=Pd[n]}r.fill(0)}var Er=class e extends Fe.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Dt.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Fe.u32)(this.state)}keccak(){Fe.isLE||(0,Fe.byteSwap32)(this.state32),Ll(this.state32,this.rounds),Fe.isLE||(0,Fe.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,Dt.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Fe.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,Dt.aexists)(this,!1),(0,Dt.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,Dt.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,Dt.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};G.Keccak=Er;var He=(e,t,r)=>(0,Fe.wrapConstructor)(()=>new Er(t,e,r));G.sha3_224=He(6,144,224/8);G.sha3_256=He(6,136,256/8);G.sha3_384=He(6,104,384/8);G.sha3_512=He(6,72,512/8);G.keccak_224=He(1,144,224/8);G.keccak_256=He(1,136,256/8);G.keccak_384=He(1,104,384/8);G.keccak_512=He(1,72,512/8);var Ul=(e,t,r)=>(0,Fe.wrapXOFConstructorWithOpts)((n={})=>new Er(t,e,n.dkLen===void 0?r:n.dkLen,!0));G.shake128=Ul(31,168,128/8);G.shake256=Ul(31,136,256/8)});var Gl=ie((UN,Ge)=>{"use strict";u();c();p();m();d();l();var{sha3_512:Td}=Fl(),$l=24,xr=32,to=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function ql(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var Bl=(e="")=>ql(Td(e)).toString(36).slice(1),Vl=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),vd=e=>Vl[Math.floor(e()*Vl.length)],jl=({globalObj:e=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+to(xr,t):to(xr,t);return Bl(n).substring(0,xr)},Ql=e=>()=>e++,Ad=476782367,Hl=({random:e=Math.random,counter:t=Ql(Math.floor(e()*Ad)),length:r=$l,fingerprint:n=jl({random:e})}={})=>function(){let o=vd(e),s=Date.now().toString(36),a=t().toString(36),f=to(r,e),w=`${s+f+a+n}`;return`${o+Bl(w).substring(1,r)}`},Cd=Hl(),Rd=(e,{minLength:t=2,maxLength:r=xr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};Ge.exports.getConstants=()=>({defaultLength:$l,bigLength:xr});Ge.exports.init=Hl;Ge.exports.createId=Cd;Ge.exports.bufToBigInt=ql;Ge.exports.createCounter=Ql;Ge.exports.createFingerprint=jl;Ge.exports.isCuid=Rd});var Jl=ie((QN,Pr)=>{"use strict";u();c();p();m();d();l();var{createId:Id,init:Sd,getConstants:kd,isCuid:Od}=Gl();Pr.exports.createId=Id;Pr.exports.init=Sd;Pr.exports.getConstants=kd;Pr.exports.isCuid=Od});u();c();p();m();d();l();var Yo={};Ht(Yo,{defineExtension:()=>Ko,getExtensionContext:()=>zo});u();c();p();m();d();l();u();c();p();m();d();l();function Ko(e){return typeof e=="function"?e:t=>t.$extends(e)}u();c();p();m();d();l();function zo(e){return e}var Xo={};Ht(Xo,{validator:()=>Zo});u();c();p();m();d();l();u();c();p();m();d();l();function Zo(...e){return t=>t}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var pi,es,ts,rs,ns=!0;typeof g<"u"&&({FORCE_COLOR:pi,NODE_DISABLE_COLORS:es,NO_COLOR:ts,TERM:rs}=g.env||{},ns=g.stdout&&g.stdout.isTTY);var Pc={enabled:!es&&ts==null&&rs!=="dumb"&&(pi!=null&&pi!=="0"||ns)};function j(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!Pc.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var jg=j(0,0),Yr=j(1,22),Zr=j(2,22),Qg=j(3,23),Xr=j(4,24),Hg=j(7,27),Gg=j(8,28),Jg=j(9,29),Wg=j(30,39),dt=j(31,39),is=j(32,39),os=j(33,39),ss=j(34,39),Kg=j(35,39),as=j(36,39),zg=j(37,39),ls=j(90,39),Yg=j(90,39),Zg=j(40,49),Xg=j(41,49),ey=j(42,49),ty=j(43,49),ry=j(44,49),ny=j(45,49),iy=j(46,49),oy=j(47,49);u();c();p();m();d();l();var Tc=100,us=["green","yellow","blue","magenta","cyan","red"],Jt=[],cs=Date.now(),vc=0,mi=typeof g<"u"?g.env:{};globalThis.DEBUG??=mi.DEBUG??"";globalThis.DEBUG_COLORS??=mi.DEBUG_COLORS?mi.DEBUG_COLORS==="true":!0;var Wt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function Ac(e){let t={color:us[vc++%us.length],enabled:Wt.enabled(e),namespace:e,log:Wt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Jt.push([o,...n]),Jt.length>Tc&&Jt.shift(),Wt.enabled(o)||i){let f=n.map(A=>typeof A=="string"?A:Cc(A)),w=`+${Date.now()-cs}ms`;cs=Date.now(),a(o,...f,w)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var K=new Proxy(Ac,{get:(e,t)=>Wt[t],set:(e,t,r)=>Wt[t]=r});function Cc(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function ps(e=7500){let t=Jt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function ms(){Jt.length=0}u();c();p();m();d();l();u();c();p();m();d();l();var Xc=Es(),fi=Xc.version;u();c();p();m();d();l();function ft(e){let t=ep();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":tp(e))}function ep(){let e=g.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function tp(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}u();c();p();m();d();l();u();c();p();m();d();l();function tn(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}u();c();p();m();d();l();var O={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};u();c();p();m();d();l();var Ts="prisma+postgres",sn=`${Ts}:`;function an(e){return e?.toString().startsWith(`${sn}//`)??!1}function hi(e){if(!an(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")||t.includes("[::1]")}var Zt={};Ht(Zt,{error:()=>op,info:()=>ip,log:()=>np,query:()=>sp,should:()=>Cs,tags:()=>Yt,warn:()=>wi});u();c();p();m();d();l();var Yt={error:dt("prisma:error"),warn:os("prisma:warn"),info:as("prisma:info"),query:ss("prisma:query")},Cs={warn:()=>!g.env.PRISMA_DISABLE_WARNINGS};function np(...e){console.log(...e)}function wi(e,...t){Cs.warn()&&console.warn(`${Yt.warn} ${e}`,...t)}function ip(e,...t){console.info(`${Yt.info} ${e}`,...t)}function op(e,...t){console.error(`${Yt.error} ${e}`,...t)}function sp(e,...t){console.log(`${Yt.query} ${e}`,...t)}u();c();p();m();d();l();function Ne(e,t){throw new Error(t)}u();c();p();m();d();l();function Ei(e,t){return Object.prototype.hasOwnProperty.call(e,t)}u();c();p();m();d();l();function un(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}u();c();p();m();d();l();function xi(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}u();c();p();m();d();l();function D(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}u();c();p();m();d();l();var Os=new Set,cn=(e,t,...r)=>{Os.has(e)||(Os.add(e),wi(t,...r))};var F=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};D(F,"PrismaClientInitializationError");u();c();p();m();d();l();var ee=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};D(ee,"PrismaClientKnownRequestError");u();c();p();m();d();l();var de=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};D(de,"PrismaClientRustPanicError");u();c();p();m();d();l();var oe=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};D(oe,"PrismaClientUnknownRequestError");u();c();p();m();d();l();var se=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};D(se,"PrismaClientValidationError");u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var Ie=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};u();c();p();m();d();l();function qe(e){return e.substring(0,1).toLowerCase()+e.substring(1)}u();c();p();m();d();l();function _s(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}u();c();p();m();d();l();function Xt(e){let t;return{get(){return t||(t={value:e()}),t.value}}}u();c();p();m();d();l();function lp(e){return{models:Pi(e.models),enums:Pi(e.enums),types:Pi(e.types)}}function Pi(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}u();c();p();m();d();l();function yt(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function pn(e){return e.toString()!=="Invalid Date"}u();c();p();m();d();l();l();function ht(e){return v.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}u();c();p();m();d();l();u();c();p();m();d();l();var mn={};Ht(mn,{ModelAction:()=>er,datamodelEnumToSchemaEnum:()=>up});u();c();p();m();d();l();u();c();p();m();d();l();function up(e){return{name:e.name,values:e.values.map(t=>t.name)}}u();c();p();m();d();l();var er=(B=>(B.findUnique="findUnique",B.findUniqueOrThrow="findUniqueOrThrow",B.findFirst="findFirst",B.findFirstOrThrow="findFirstOrThrow",B.findMany="findMany",B.create="create",B.createMany="createMany",B.createManyAndReturn="createManyAndReturn",B.update="update",B.updateMany="updateMany",B.updateManyAndReturn="updateManyAndReturn",B.upsert="upsert",B.delete="delete",B.deleteMany="deleteMany",B.groupBy="groupBy",B.count="count",B.aggregate="aggregate",B.findRaw="findRaw",B.aggregateRaw="aggregateRaw",B))(er||{});var cp=Ae(As());var pp={red:dt,gray:ls,dim:Zr,bold:Yr,underline:Xr,highlightSource:e=>e.highlight()},mp={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function dp({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function fp({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],f=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${f}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${f}`)),t&&a.push(s.underline(gp(t))),i){a.push("");let w=[i.toString()];o&&(w.push(o),w.push(s.dim(")"))),a.push(w.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function gp(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function dn(e){let t=e.showColors?pp:mp,r;return typeof $getTemplateParameters<"u"?r=$getTemplateParameters(e,t):r=dp(e),fp(r,t)}u();c();p();m();d();l();var Bs=Ae(Ti());u();c();p();m();d();l();function Us(e,t,r){let n=Fs(e),i=yp(n),o=wp(i);o?fn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Fs(e){return e.errors.flatMap(t=>t.kind==="Union"?Fs(t):[t])}function yp(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:hp(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function hp(e,t){return[...new Set(e.concat(t))]}function wp(e){return xi(e,(t,r)=>{let n=Ns(t),i=Ns(r);return n!==i?n-i:Ls(t)-Ls(r)})}function Ns(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Ls(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}u();c();p();m();d();l();var he=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};u();c();p();m();d();l();u();c();p();m();d();l();$s();u();c();p();m();d();l();var wt=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};Vs();u();c();p();m();d();l();u();c();p();m();d();l();var gn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};u();c();p();m();d();l();var yn=e=>e,hn={bold:yn,red:yn,green:yn,dim:yn,enabled:!1},qs={bold:Yr,red:dt,green:is,dim:Zr,enabled:!0},bt={write(e){e.writeLine(",")}};u();c();p();m();d();l();var Se=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};u();c();p();m();d();l();var Be=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var Et=class extends Be{items=[];addItem(t){return this.items.push(new gn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Se("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(bt,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var xt=class e extends Be{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof Et&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Se("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(bt,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};u();c();p();m();d();l();var re=class extends Be{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Se(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};u();c();p();m();d();l();var tr=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(bt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function fn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":bp(e,t);break;case"IncludeOnScalar":Ep(e,t);break;case"EmptySelection":xp(e,t,r);break;case"UnknownSelectionField":Ap(e,t);break;case"InvalidSelectionValue":Cp(e,t);break;case"UnknownArgument":Rp(e,t);break;case"UnknownInputField":Ip(e,t);break;case"RequiredArgumentMissing":Sp(e,t);break;case"InvalidArgumentType":kp(e,t);break;case"InvalidArgumentValue":Op(e,t);break;case"ValueTooLarge":Dp(e,t);break;case"SomeFieldsMissing":_p(e,t);break;case"TooManyFieldsGiven":Mp(e,t);break;case"Union":Us(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function bp(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function Ep(e,t){let[r,n]=Pt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new he(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${rr(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function xp(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){Pp(e,t,i);return}if(n.hasField("select")){Tp(e,t);return}}if(r?.[qe(e.outputType.name)]){vp(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function Pp(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new he(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function Tp(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Hs(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${rr(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function vp(e,t){let r=new tr;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new he("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=Pt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let f=a?.value.asObject()??new xt;f.addSuggestion(n),a.value=f}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function Ap(e,t){let r=Gs(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Hs(n,e.outputType);break;case"include":Np(n,e.outputType);break;case"omit":Lp(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(rr(n)),i.join(" ")})}function Cp(e,t){let r=Gs(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Rp(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),Up(n,e.arguments)),t.addErrorMessage(i=>js(i,r,e.arguments.map(o=>o.name)))}function Ip(e,t){let[r,n]=Pt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&Js(o,e.inputType)}t.addErrorMessage(o=>js(o,n,e.inputType.fields.map(s=>s.name)))}function js(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=Vp(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(rr(e)),n.join(" ")}function Sp(e,t){let r;t.addErrorMessage(f=>r?.value instanceof re&&r.value.text==="null"?`Argument \`${f.green(o)}\` must not be ${f.red("null")}.`:`Argument \`${f.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=Pt(e.argumentPath),s=new tr,a=n.getDeepFieldValue(i)?.asObject();if(a){if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let f of e.inputTypes[0].fields)s.addField(f.name,f.typeNames.join(" | "));a.addSuggestion(new he(o,s).makeRequired())}else{let f=e.inputTypes.map(Qs).join(" | ");a.addSuggestion(new he(o,f).makeRequired())}if(e.dependentArgumentPath){n.getDeepField(e.dependentArgumentPath)?.markAsError();let[,f]=Pt(e.dependentArgumentPath);t.addErrorMessage(w=>`Argument \`${w.green(o)}\` is required because argument \`${w.green(f)}\` was provided.`)}}}function Qs(e){return e.kind==="list"?`${Qs(e.elementType)}[]`:e.name}function kp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=wn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Op(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=wn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Dp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof re&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function _p(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&Js(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${wn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(rr(i)),o.join(" ")})}function Mp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${wn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Hs(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new he(r.name,"true"))}function Np(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new he(r.name,"true"))}function Lp(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new he(r.name,"true"))}function Up(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new he(r.name,r.typeNames.join(" | ")))}function Gs(e,t){let[r,n]=Pt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),f=o?.getField(n);return o&&f?{parentKind:"select",parent:o,field:f,fieldName:n}:(f=s?.getField(n),s&&f?{parentKind:"include",field:f,parent:s,fieldName:n}:(f=a?.getField(n),a&&f?{parentKind:"omit",field:f,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function Js(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new he(r.name,r.typeNames.join(" | ")))}function Pt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function rr({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function wn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Fp=3;function Vp(e,t){let r=1/0,n;for(let i of t){let o=(0,Bs.default)(e,i);o>Fp||o<r&&(r=o,n=i)}return n}u();c();p();m();d();l();u();c();p();m();d();l();var nr=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function Tt(e){return e instanceof nr}u();c();p();m();d();l();var bn=Symbol(),Ai=new WeakMap,Le=class{constructor(t){t===bn?Ai.set(this,`Prisma.${this._getName()}`):Ai.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Ai.get(this)}},ir=class extends Le{_getNamespace(){return"NullTypes"}},or=class extends ir{#e};Ri(or,"DbNull");var sr=class extends ir{#e};Ri(sr,"JsonNull");var ar=class extends ir{#e};Ri(ar,"AnyNull");var Ci={classes:{DbNull:or,JsonNull:sr,AnyNull:ar},instances:{DbNull:new or(bn),JsonNull:new sr(bn),AnyNull:new ar(bn)}};function Ri(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}u();c();p();m();d();l();var Ws=": ",En=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Ws.length}write(t){let r=new Se(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Ws).write(this.value)}};var Ii=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function vt(e){return new Ii(Ks(e))}function Ks(e){let t=new xt;for(let[r,n]of Object.entries(e)){let i=new En(r,zs(n));t.addField(i)}return t}function zs(e){if(typeof e=="string")return new re(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new re(String(e));if(typeof e=="bigint")return new re(`${e}n`);if(e===null)return new re("null");if(e===void 0)return new re("undefined");if(ht(e))return new re(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return y.isBuffer(e)?new re(`Buffer.alloc(${e.byteLength})`):new re(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=pn(e)?e.toISOString():"Invalid Date";return new re(`new Date("${t}")`)}return e instanceof Le?new re(`Prisma.${e._getName()}`):Tt(e)?new re(`prisma.${qe(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?$p(e):typeof e=="object"?Ks(e):new re(Object.prototype.toString.call(e))}function $p(e){let t=new Et;for(let r of e)t.addItem(zs(r));return t}function xn(e,t){let r=t==="pretty"?qs:hn,n=e.renderAllMessages(r),i=new wt(0,{colors:r}).write(e).toString();return{message:n,args:i}}function Pn({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=vt(e);for(let C of t)fn(C,a,s);let{message:f,args:w}=xn(a,r),A=dn({message:f,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:w});throw new se(A,{clientVersion:o})}u();c();p();m();d();l();u();c();p();m();d();l();function ke(e){return e.replace(/^./,t=>t.toLowerCase())}u();c();p();m();d();l();function Zs(e,t,r){let n=ke(r);return!t.result||!(t.result.$allModels||t.result[n])?e:qp({...e,...Ys(t.name,e,t.result.$allModels),...Ys(t.name,e,t.result[n])})}function qp(e){let t=new Ie,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return un(e,n=>({...n,needs:r(n.name,new Set)}))}function Ys(e,t,r){return r?un(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Bp(t,o,i)})):{}}function Bp(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Xs(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function ea(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var Tn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new Ie;modelExtensionsCache=new Ie;queryCallbacksCache=new Ie;clientExtensions=Xt(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=Xt(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>Zs(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=ke(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},At=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new Tn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new Tn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};u();c();p();m();d();l();var vn=class{constructor(t){this.name=t}};function ta(e){return e instanceof vn}function jp(e){return new vn(e)}u();c();p();m();d();l();u();c();p();m();d();l();var ra=Symbol(),lr=class{constructor(t){if(t!==ra)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?Si:t}},Si=new lr(ra);function Oe(e){return e instanceof lr}var Qp={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},na="explicitly `undefined` values are not allowed";function Oi({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=At.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:f,previewFeatures:w,globalOmit:A}){let C=new ki({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:f,previewFeatures:w,globalOmit:A});return{modelName:e,action:Qp[t],query:ur(r,C)}}function ur({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:oa(r,n),selection:Hp(e,t,i,n)}}function Hp(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Kp(e,n)):Gp(n,t,r)}function Gp(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&Jp(n,t,e),Wp(n,r,e),n}function Jp(e,t,r){for(let[n,i]of Object.entries(t)){if(Oe(i))continue;let o=r.nestSelection(n);if(Di(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=ur(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=ur(i,o)}}function Wp(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=ea(i,n);for(let[s,a]of Object.entries(o)){if(Oe(a))continue;Di(a,r.nestSelection(s));let f=r.findField(s);n?.[s]&&!f||(e[s]=!a)}}function Kp(e,t){let r={},n=t.getComputedFields(),i=Xs(e,n);for(let[o,s]of Object.entries(i)){if(Oe(s))continue;let a=t.nestSelection(o);Di(s,a);let f=t.findField(o);if(!(n?.[o]&&!f)){if(s===!1||s===void 0||Oe(s)){r[o]=!1;continue}if(s===!0){f?.kind==="object"?r[o]=ur({},a):r[o]=!0;continue}r[o]=ur(s,a)}}return r}function ia(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(yt(e)){if(pn(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(ta(e))return{$type:"Param",value:e.name};if(Tt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return zp(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:y.from(r,n,i).toString("base64")}}if(Yp(e))return e.values;if(ht(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Le){if(e!==Ci.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Zp(e))return e.toJSON();if(typeof e=="object")return oa(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function oa(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);Oe(i)||(i!==void 0?r[n]=ia(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:na}))}return r}function zp(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||Oe(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(ia(o,i))}return r}function Yp(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Zp(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function Di(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:na})}var ki=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){Pn({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[qe(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:Ne(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};u();c();p();m();d();l();function sa(e){if(!e._hasPreviewFlag("metrics"))throw new se("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var cr=class{_client;constructor(t){this._client=t}prometheus(t){return sa(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return sa(this._client),this._client._engine.metrics({format:"json",...t})}};u();c();p();m();d();l();function Xp(e,t){let r=Xt(()=>em(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function em(e){throw new Error("Prisma.dmmf is not available when running in edge runtimes.")}function _i(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}u();c();p();m();d();l();var Mi=new WeakMap,An="$$PrismaTypedSql",pr=class{constructor(t,r){Mi.set(this,{sql:t,values:r}),Object.defineProperty(this,An,{value:An})}get sql(){return Mi.get(this).sql}get values(){return Mi.get(this).values}};function tm(e){return(...t)=>new pr(e,t)}function Cn(e){return e!=null&&e[An]===An}u();c();p();m();d();l();var nc=Ae(yi());u();c();p();m();d();l();aa();fs();bs();u();c();p();m();d();l();var we=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let f=0;for(;f<s.values.length;)this.values[o++]=s.values[f++],this.strings[o]=s.strings[f];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function rm(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new we([r,...Array(e.length-1).fill(t),n],e)}function la(e){return new we([e],[])}var nm=la("");function ua(e,...t){return new we(e,t)}u();c();p();m();d();l();u();c();p();m();d();l();function mr(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}u();c();p();m();d();l();function ae(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}u();c();p();m();d();l();function Xe(e){let t=new Ie;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}u();c();p();m();d();l();u();c();p();m();d();l();var In={enumerable:!0,configurable:!0,writable:!0};function Sn(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>In,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var ca=Symbol.for("nodejs.util.inspect.custom");function Pe(e,t){let r=im(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=pa(Reflect.ownKeys(o),r),a=pa(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let f=r.get(s);return f?f.getPropertyDescriptor?{...In,...f?.getPropertyDescriptor(s)}:In:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[ca]=function(){let o={...this};return delete o[ca],o},i}function im(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function pa(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}u();c();p();m();d();l();function Ct(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}u();c();p();m();d();l();function Rt(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}u();c();p();m();d();l();function ma(e){if(e===void 0)return"";let t=vt(e);return new wt(0,{colors:hn}).write(t).toString()}u();c();p();m();d();l();var om="P2037";function kn({error:e,user_facing_error:t},r,n){return t.error_code?new ee(sm(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new oe(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function sm(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===om&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var Ni=class{getLocation(){return null}};function je(e){return typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Ni}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var da={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function It(e={}){let t=lm(e);return Object.entries(t).reduce((n,[i,o])=>(da[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function lm(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function On(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function fa(e,t){let r=On(e);return t({action:"aggregate",unpacker:r,argsMapper:It})(e)}u();c();p();m();d();l();function um(e={}){let{select:t,...r}=e;return typeof t=="object"?It({...r,_count:t}):It({...r,_count:{_all:!0}})}function cm(e={}){return typeof e.select=="object"?t=>On(e)(t)._count:t=>On(e)(t)._count._all}function ga(e,t){return t({action:"count",unpacker:cm(e),argsMapper:um})(e)}u();c();p();m();d();l();function pm(e={}){let t=It(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function mm(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function ya(e,t){return t({action:"groupBy",unpacker:mm(e),argsMapper:pm})(e)}function ha(e,t,r){if(t==="aggregate")return n=>fa(n,r);if(t==="count")return n=>ga(n,r);if(t==="groupBy")return n=>ya(n,r)}u();c();p();m();d();l();function wa(e,t){let r=t.fields.filter(i=>!i.relationName),n=_s(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new nr(e,o,s.type,s.isList,s.kind==="enum")},...Sn(Object.keys(n))})}u();c();p();m();d();l();u();c();p();m();d();l();var ba=e=>Array.isArray(e)?e:e.split("."),Li=(e,t)=>ba(t).reduce((r,n)=>r&&r[n],e),Ea=(e,t,r)=>ba(t).reduceRight((n,i,o,s)=>Object.assign({},Li(e,s.slice(0,o)),{[i]:n}),r);function dm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function fm(e,t,r){return t===void 0?e??{}:Ea(t,r,e||!0)}function Ui(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((f,w)=>({...f,[w.name]:w}),{});return f=>{let w=je(e._errorFormat),A=dm(n,i),C=fm(f,o,A),I=r({dataPath:A,callsite:w})(C),R=gm(e,t);return new Proxy(I,{get(L,k){if(!R.includes(k))return L[k];let _e=[a[k].type,r,k],ue=[A,C];return Ui(e,..._e,...ue)},...Sn([...R,...Object.getOwnPropertyNames(I)])})}}function gm(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var ym=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],hm=["aggregate","count","groupBy"];function Fi(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[wm(e,t),Em(e,t),mr(r),ae("name",()=>t),ae("$name",()=>t),ae("$parent",()=>e._appliedParent)];return Pe({},n)}function wm(e,t){let r=ke(t),n=Object.keys(er).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>f=>{let w=je(e._errorFormat);return e._createPrismaPromise(A=>{let C={args:f,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:A,callsite:w};return e._request({...C,...a})},{action:o,args:f,model:t})};return ym.includes(o)?Ui(e,t,s):bm(i)?ha(e,i,s):s({})}}}function bm(e){return hm.includes(e)}function Em(e,t){return Xe(ae("fields",()=>{let r=e._runtimeDataModel.models[t];return wa(t,r)}))}u();c();p();m();d();l();function xa(e){return e.replace(/^./,t=>t.toUpperCase())}var Vi=Symbol();function dr(e){let t=[xm(e),Pm(e),ae(Vi,()=>e),ae("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(mr(r)),Pe(e,t)}function xm(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function Pm(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(ke),n=[...new Set(t.concat(r))];return Xe({getKeys(){return n},getPropertyValue(i){let o=xa(i);if(e._runtimeDataModel.models[o]!==void 0)return Fi(e,o);if(e._runtimeDataModel.models[i]!==void 0)return Fi(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function Pa(e){return e[Vi]?e[Vi]:e}function Ta(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$on:{value:void 0}});return dr(t)}u();c();p();m();d();l();u();c();p();m();d();l();function va({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let f of Object.values(o)){if(n){if(n[f.name])continue;let w=f.needs.filter(A=>n[A]);w.length>0&&a.push(Ct(w))}else if(r){if(!r[f.name])continue;let w=f.needs.filter(A=>!r[A]);w.length>0&&a.push(Ct(w))}Tm(e,f.needs)&&s.push(vm(f,Pe(e,s)))}return s.length>0||a.length>0?Pe(e,[...s,...a]):e}function Tm(e,t){return t.every(r=>Ei(e,r))}function vm(e,t){return Xe(ae(e.name,()=>e.compute(t)))}u();c();p();m();d();l();function Dn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Dn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Aa({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Aa({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Aa({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||Oe(s))continue;let f=n.models[r].fields.find(A=>A.name===o);if(!f||f.kind!=="object"||!f.relationName)continue;let w=typeof s=="object"?s:{};t[o]=Dn({visitor:i,result:t[o],args:w,modelName:f.type,runtimeDataModel:n})}}function Ca({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:Dn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,f,w)=>{let A=ke(f);return va({result:a,modelName:A,select:w.select,omit:w.select?void 0:{...o?.[A],...w.omit},extensions:n})}})}u();c();p();m();d();l();u();c();p();m();d();l();l();u();c();p();m();d();l();var Am=["$connect","$disconnect","$on","$transaction","$extends"],Ra=Am;function Ia(e){if(e instanceof we)return Cm(e);if(Cn(e))return Rm(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=fr(e[n]);return r}let t={};for(let r in e)t[r]=fr(e[r]);return t}function Cm(e){return new we(e.strings,e.values)}function Rm(e){return new pr(e.sql,e.values)}function fr(e){if(typeof e!="object"||e==null||e instanceof Le||Tt(e))return e;if(ht(e))return new me(e.toFixed());if(yt(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=fr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:fr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=fr(e[r]);return t}Ne(e,"Unknown value")}function ka(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Ia(t.args??{}),__internalParams:t,query:(s,a=t)=>{let f=a.customDataProxyFetch;return a.customDataProxyFetch=Ma(o,f),a.args=s,ka(e,a,r,n+1)}})})}function Oa(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return ka(e,t,s)}function Da(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?_a(r,n,0,e):e(r)}}function _a(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let f=a.customDataProxyFetch;return a.customDataProxyFetch=Ma(i,f),_a(a,t,r+1,n)}})}var Sa=e=>e;function Ma(e=Sa,t=Sa){return r=>e(t(r))}u();c();p();m();d();l();var Na=K("prisma:client"),La={Vercel:"vercel","Netlify CI":"netlify"};function Ua({postinstall:e,ciName:t,clientVersion:r}){if(Na("checkPlatformCaching:postinstall",e),Na("checkPlatformCaching:ciName",t),e===!0&&t&&t in La){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${La[t]}-build`;throw console.error(n),new F(n,r)}}u();c();p();m();d();l();function Fa(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}u();c();p();m();d();l();u();c();p();m();d();l();var Im=()=>globalThis.process?.release?.name==="node",Sm=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,km=()=>!!globalThis.Deno,Om=()=>typeof globalThis.Netlify=="object",Dm=()=>typeof globalThis.EdgeRuntime=="object",_m=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function Mm(){return[[Om,"netlify"],[Dm,"edge-light"],[_m,"workerd"],[km,"deno"],[Sm,"bun"],[Im,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var Nm={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function gr(){let e=Mm();return{id:e,prettyName:Nm[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}u();c();p();m();d();l();u();c();p();m();d();l();var $i=Ae(bi());u();c();p();m();d();l();function Va(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}u();c();p();m();d();l();function $a(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}u();c();p();m();d();l();var qa=Ae(ks());function Ba({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,qa.default)({user:t,repo:r,template:n,title:e,body:i})}function ja({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=ps(6e3-(s?.length??0)),f=$a((0,$i.default)(a)),w=n?`# Description
\`\`\`
${n}
\`\`\``:"",A=(0,$i.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${g.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${w}

## Logs
\`\`\`
${f}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?Va(s):""}
\`\`\`
`),C=Ba({title:r,body:A});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${Xr(C)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();l();u();c();p();m();d();l();l();function $(e,t){throw new Error(t)}function qi(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>qi(e[r],t[r]))}function St(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]==typeof t[o]&&typeof e[o]!="object")return e[o]===t[o];if(me.isDecimal(e[o])||me.isDecimal(t[o])){let s=Qa(e[o]),a=Qa(t[o]);return s&&a&&s.equals(a)}else if(e[o]instanceof Uint8Array||t[o]instanceof Uint8Array){let s=Ha(e[o]),a=Ha(t[o]);return s&&a&&s.equals(a)}else{if(e[o]instanceof Date||t[o]instanceof Date)return Ga(e[o])?.getTime()===Ga(t[o])?.getTime();if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return Ja(e[o])===Ja(t[o]);if(typeof e[o]=="number"||typeof t[o]=="number")return Wa(e[o])===Wa(t[o])}return qi(e[o],t[o])})}function Qa(e){return me.isDecimal(e)?e:typeof e=="number"||typeof e=="string"?new me(e):void 0}function Ha(e){return y.isBuffer(e)?e:e instanceof Uint8Array?y.from(e.buffer,e.byteOffset,e.byteLength):typeof e=="string"?y.from(e,"base64"):void 0}function Ga(e){return e instanceof Date?e:typeof e=="string"||typeof e=="number"?new Date(e):void 0}function Ja(e){return typeof e=="bigint"?e:typeof e=="number"||typeof e=="string"?BigInt(e):void 0}function Wa(e){return typeof e=="number"?e:typeof e=="string"?Number(e):void 0}function yr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?y.from(r).toString("base64"):r)}function Lm(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Um(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function et(e){return e===null?e:Array.isArray(e)?e.map(et):typeof e=="object"?Lm(e)?Fm(e):e.constructor!==null&&e.constructor.name!=="Object"?e:Um(e,et):e}function Fm({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new v(t);case"Json":return JSON.parse(t);default:$(t,"Unknown tagged value")}}u();c();p();m();d();l();var le=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function kt(e){if(!tn(e))throw e;let t=Vm(e),r=Ka(e);throw!t||!r?e:new le(r,t,{driverAdapterError:e})}function ji(e){throw tn(e)?new le(`Raw query failed. Code: \`${e.cause.originalCode??"N/A"}\`. Message: \`${e.cause.originalMessage??Ka(e)}\``,"P2010",{driverAdapterError:e}):e}function Vm(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseNotReachable":return"P1001";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"TlsConnectionError":return"P1011";case"ConnectionClosed":return"P1017";case"TransactionAlreadyClosed":return"P1018";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"ValueOutOfRange":return"P2020";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":case"InconsistentColumnData":return"P2023";case"MissingFullTextSearchIndex":return"P2030";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":case"mssql":return;default:$(e.cause,`Unknown error: ${e.cause}`)}}function Ka(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseNotReachable":{let t=e.cause.host&&e.cause.port?`${e.cause.host}:${e.cause.port}`:e.cause.host;return`Can't reach database server${t?` at ${t}`:""}`}case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"TlsConnectionError":return`Error opening a TLS connection: ${e.cause.reason}`;case"ConnectionClosed":return"Server has closed the connection.";case"TransactionAlreadyClosed":return e.cause.cause;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${Bi(e.cause.constraint)}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${Bi(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${Bi(e.cause.constraint)}`;case"ValueOutOfRange":return`Value out of range for the type: ${e.cause.cause}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Error in connector: Conversion error: ${e.cause.level}`;case"InconsistentColumnData":return`Inconsistent column data: ${e.cause.cause}`;case"MissingFullTextSearchIndex":return"Cannot find a fulltext index to use for the native search, try adding a @@fulltext([Fields...]) to your schema";case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":case"mssql":return;default:$(e.cause,`Unknown error: ${e.cause}`)}}function Bi(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}function za(e,t){let r=e.map(i=>t.keys.reduce((o,s)=>(o[s]=et(i[s]),o),{})),n=new Set(t.nestedSelection);return t.arguments.map(i=>{let o=r.findIndex(s=>St(s,i));if(o===-1)return t.expectNonEmpty?new le("An operation failed because it depends on one or more records that were required but not found","P2025"):null;{let s=Object.entries(e[o]).filter(([a])=>n.has(a));return Object.fromEntries(s)}})}u();c();p();m();d();l();l();var W=class extends Error{name="DataMapperError"};function Xa(e,t,r){switch(t.type){case"AffectedRows":if(typeof e!="number")throw new W(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"Object":return Qi(e,t.fields,r,t.skipNulls);case"Value":return Hi(e,"<result>",t.resultType,r);default:$(t,`Invalid data mapping type: '${t.type}'`)}}function Qi(e,t,r,n){if(e===null)return null;if(Array.isArray(e)){let i=e;return n&&(i=i.filter(o=>o!==null)),i.map(o=>Ya(o,t,r))}if(typeof e=="object")return Ya(e,t,r);if(typeof e=="string"){let i;try{i=JSON.parse(e)}catch(o){throw new W("Expected an array or object, got a string that is not valid JSON",{cause:o})}return Qi(i,t,r,n)}throw new W(`Expected an array or an object, got: ${typeof e}`)}function Ya(e,t,r){if(typeof e!="object")throw new W(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"AffectedRows":throw new W(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"Object":{if(o.serializedName!==null&&!Object.hasOwn(e,o.serializedName))throw new W(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.serializedName!==null?e[o.serializedName]:e;n[i]=Qi(s,o.fields,r,o.skipNulls);break}case"Value":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=Hi(e[s],s,o.resultType,r);else throw new W(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:$(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function Hi(e,t,r,n){if(e===null)return r.type==="Array"?[]:null;switch(r.type){case"Any":return e;case"String":{if(typeof e!="string")throw new W(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"Int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new W(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new W(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new W(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"BigInt":{if(typeof e!="number"&&typeof e!="string")throw new W(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"Float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new W(`Expected a float in column '${t}', got string: ${e}`);return i}throw new W(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"Boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new W(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}if(e instanceof Uint8Array){for(let i of e)if(i!==0)return!0;return!1}throw new W(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"Decimal":if(typeof e!="number"&&typeof e!="string"&&!me.isDecimal(e))throw new W(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"Date":{if(typeof e=="string")return{$type:"DateTime",value:Za(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new W(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"Time":{if(typeof e=="string")return{$type:"DateTime",value:`1970-01-01T${Za(e)}`};throw new W(`Expected a time in column '${t}', got ${typeof e}: ${e}`)}case"Array":return e.map((o,s)=>Hi(o,`${t}[${s}]`,r.inner,n));case"Object":return{$type:"Json",value:yr(e)};case"Json":return{$type:"Json",value:`${e}`};case"Bytes":{if(typeof e=="string"&&e.startsWith("\\x"))return{$type:"Bytes",value:y.from(e.slice(2),"hex").toString("base64")};if(Array.isArray(e))return{$type:"Bytes",value:y.from(e).toString("base64")};if(e instanceof Uint8Array)return{$type:"Bytes",value:y.from(e).toString("base64")};throw new W(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`)}case"Enum":{let i=n[r.inner];if(i===void 0)throw new W(`Unknown enum '${r.inner}'`);let o=i[`${e}`];if(o===void 0)throw new W(`Value '${e}' not found in enum '${r.inner}'`);return o}default:$(r,`DataMapper: Unknown result type: ${r.type}`)}}var $m=/Z$|(?<!\d{4}-\d{2})[+-]\d{2}(:?\d{2})?$/;function Za(e){let t=$m.exec(e);return t===null?`${e}Z`:t[0]!=="Z"&&t[1]===void 0?`${e}:00`:e}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var hr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(hr||(hr={}));function qm(e){switch(e){case"postgresql":case"postgres":case"prisma+postgres":return"postgresql";case"sqlserver":return"mssql";case"mysql":case"sqlite":case"cockroachdb":case"mongodb":return e;default:$(e,`Unknown provider: ${e}`)}}async function _n({query:e,tracingHelper:t,provider:r,onQuery:n,execute:i}){return await t.runInChildSpan({name:"db_query",kind:hr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":qm(r)}},async()=>{let o=new Date,s=b.now(),a=await i(),f=b.now();return n?.({timestamp:o,duration:f-s,query:e.sql,params:e.args}),a})}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();function tt(e,t){var r="000000000"+e;return r.substr(r.length-t)}var el=Ae(gs(),1);function Bm(){try{return el.default.hostname()}catch{return g.env._CLUSTER_NETWORK_NAME_||g.env.COMPUTERNAME||"hostname"}}var tl=2,jm=tt(g.pid.toString(36),tl),rl=Bm(),Qm=rl.length,Hm=tt(rl.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+Qm+36).toString(36),tl);function Gi(){return jm+Hm}u();c();p();m();d();l();u();c();p();m();d();l();function Mn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function Ji(e){let n=Math.pow(36,4),i=0;function o(){return tt((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var f="c",w=new Date().getTime().toString(36),A=tt(s().toString(36),4),C=e(),I=o()+o();return f+w+A+C+I}return a.fingerprint=e,a.isCuid=Mn,a}var Gm=Ji(Gi);var nl=Gm;var eu=Ae(Jl());u();c();p();m();d();l();Ze();u();c();p();m();d();l();var Wl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var Dd=128,nt,_t;function _d(e){!nt||nt.length<e?(nt=y.allocUnsafe(e*Dd),zt.getRandomValues(nt),_t=0):_t+e>nt.length&&(zt.getRandomValues(nt),_t=0),_t+=e}function ro(e=21){_d(e|=0);let t="";for(let r=_t-e;r<_t;r++)t+=Wl[nt[r]&63];return t}u();c();p();m();d();l();Ze();var zl="0123456789ABCDEFGHJKMNPQRSTVWXYZ",Tr=32;var Md=16,Yl=10,Kl=0xffffffffffff;var it;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(it||(it={}));var ot=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function Nd(e){let t=Math.floor(e()*Tr);return t===Tr&&(t=Tr-1),zl.charAt(t)}function Ld(e){let t=Ud(),r=t&&(t.crypto||t.msCrypto)||(typeof gt<"u"?gt:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(gt?.randomBytes)return()=>gt.randomBytes(1).readUInt8()/255;throw new ot(it.PRNGDetectFailure,"Failed to find a reliable PRNG")}function Ud(){return $d()?self:typeof window<"u"?window:typeof globalThis<"u"||typeof globalThis<"u"?globalThis:null}function Fd(e,t){let r="";for(;e>0;e--)r=Nd(t)+r;return r}function Vd(e,t=Yl){if(isNaN(e))throw new ot(it.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>Kl)throw new ot(it.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${Kl}: ${e}`);if(e<0)throw new ot(it.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new ot(it.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%Tr,n=zl.charAt(r)+n,e=(e-r)/Tr;return n}function $d(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function Zl(e,t){let r=t||Ld(),n=!e||isNaN(e)?Date.now():e;return Vd(n,Yl)+Fd(Md,r)}u();c();p();m();d();l();u();c();p();m();d();l();var ne=[];for(let e=0;e<256;++e)ne.push((e+256).toString(16).slice(1));function Fn(e,t=0){return(ne[e[t+0]]+ne[e[t+1]]+ne[e[t+2]]+ne[e[t+3]]+"-"+ne[e[t+4]]+ne[e[t+5]]+"-"+ne[e[t+6]]+ne[e[t+7]]+"-"+ne[e[t+8]]+ne[e[t+9]]+"-"+ne[e[t+10]]+ne[e[t+11]]+ne[e[t+12]]+ne[e[t+13]]+ne[e[t+14]]+ne[e[t+15]]).toLowerCase()}u();c();p();m();d();l();Ze();var $n=new Uint8Array(256),Vn=$n.length;function Mt(){return Vn>$n.length-16&&(nn($n),Vn=0),$n.slice(Vn,Vn+=16)}u();c();p();m();d();l();u();c();p();m();d();l();Ze();var no={randomUUID:rn};function qd(e,t,r){if(no.randomUUID&&!t&&!e)return no.randomUUID();e=e||{};let n=e.random??e.rng?.()??Mt();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return Fn(n)}var io=qd;u();c();p();m();d();l();var oo={};function Bd(e,t,r){let n;if(e)n=Xl(e.random??e.rng?.()??Mt(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Mt();jd(oo,i,o),n=Xl(o,oo.msecs,oo.seq,t,r)}return t??Fn(n)}function jd(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function Xl(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var so=Bd;var qn=class{#e={};constructor(){this.register("uuid",new uo),this.register("cuid",new co),this.register("ulid",new po),this.register("nanoid",new mo),this.register("product",new fo)}snapshot(t){return Object.create(this.#e,{now:{value:t==="mysql"?new lo:new ao}})}register(t,r){this.#e[t]=r}},ao=class{#e=new Date;generate(){return this.#e.toISOString()}},lo=class{#e=new Date;generate(){return this.#e.toISOString().replace("T"," ").replace("Z","")}},uo=class{generate(t){if(t===4)return io();if(t===7)return so();throw new Error("Invalid UUID generator arguments")}},co=class{generate(t){if(t===1)return nl();if(t===2)return(0,eu.createId)();throw new Error("Invalid CUID generator arguments")}},po=class{generate(){return Zl()}},mo=class{generate(t){if(typeof t=="number")return ro(t);if(t===void 0)return ro();throw new Error("Invalid Nanoid generator arguments")}},fo=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};u();c();p();m();d();l();function Bn(e,t){return e==null?e:typeof e=="string"?Bn(JSON.parse(e),t):Array.isArray(e)?Hd(e,t):Qd(e,t)}function Qd(e,t){if(t.pagination){let{skip:r,take:n,cursor:i}=t.pagination;if(r!==null&&r>0||n===0||i!==null&&!St(e,i))return null}return ru(e,t.nested)}function ru(e,t){for(let[r,n]of Object.entries(t))e[r]=Bn(e[r],n);return e}function Hd(e,t){if(t.distinct!==null){let r=t.linkingFields!==null?[...t.distinct,...t.linkingFields]:t.distinct;e=Gd(e,r)}return t.pagination&&(e=Jd(e,t.pagination,t.linkingFields)),t.reverse&&e.reverse(),Object.keys(t.nested).length===0?e:e.map(r=>ru(r,t.nested))}function Gd(e,t){let r=new Set,n=[];for(let i of e){let o=vr(i,t);r.has(o)||(r.add(o),n.push(i))}return n}function Jd(e,t,r){if(r===null)return tu(e,t);let n=new Map;for(let o of e){let s=vr(o,r);n.has(s)||n.set(s,[]),n.get(s).push(o)}let i=Array.from(n.entries());return i.sort(([o],[s])=>o<s?-1:o>s?1:0),i.flatMap(([,o])=>tu(o,t))}function tu(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>St(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function vr(e,t){return JSON.stringify(t.map(r=>e[r]))}u();c();p();m();d();l();u();c();p();m();d();l();function go(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function yo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function nu(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function iu(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bigint"}function bo(e,t,r,n){let i=e.type,o=ou(e.params,t,r);switch(i){case"rawSql":return[su(e.sql,ou(e.params,t,r))];case"templateSql":return(e.chunkable?Zd(e.fragments,o,n):[o]).map(a=>{if(n!==void 0&&a.length>n)throw new le("The query parameter limit supported by your database is exceeded.","P2029");return Wd(e.fragments,e.placeholderFormat,a)});default:$(i,"Invalid query type")}}function ou(e,t,r){return e.map(n=>Te(n,t,r))}function Te(e,t,r){let n=e;for(;Yd(n);)if(go(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(yo(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(a=>Te(a,t,r)))}else $(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)?n=n.map(i=>Te(i,t,r)):nu(n)?n=y.from(n.prisma__value,"base64"):iu(n)&&(n=BigInt(n.prisma__value)),n}function Wd(e,t,r){let n="",i={placeholderNumber:1},o=[];for(let s of wo(e,r))o.push(...au(s)),n+=Kd(s,t,i);return su(n,o)}function Kd(e,t,r){let n=e.type;switch(n){case"parameter":return ho(t,r.placeholderNumber++);case"stringChunk":return e.chunk;case"parameterTuple":return`(${e.value.length==0?"NULL":e.value.map(()=>ho(t,r.placeholderNumber++)).join(",")})`;case"parameterTupleList":return e.value.map(i=>{let o=i.map(()=>ho(t,r.placeholderNumber++)).join(e.itemSeparator);return`${e.itemPrefix}${o}${e.itemSuffix}`}).join(e.groupSeparator);default:$(n,"Invalid fragment type")}}function ho(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function su(e,t){let r=t.map(n=>zd(n));return{sql:e,args:t,argTypes:r}}function zd(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":y.isBuffer(e)?"Bytes":"Unknown"}function Yd(e){return go(e)||yo(e)}function*wo(e,t){let r=0;for(let n of e)switch(n.type){case"parameter":{if(r>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);yield{...n,value:t[r++]};break}case"stringChunk":{yield n;break}case"parameterTuple":{if(r>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);let i=t[r++];yield{...n,value:Array.isArray(i)?i:[i]};break}case"parameterTupleList":{if(r>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);let i=t[r++];if(!Array.isArray(i))throw new Error("Malformed query template. Tuple list expected.");if(i.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");for(let o of i)if(!Array.isArray(o))throw new Error("Malformed query template. Tuple expected.");yield{...n,value:i};break}}}function*au(e){switch(e.type){case"parameter":yield e.value;break;case"stringChunk":break;case"parameterTuple":yield*e.value;break;case"parameterTupleList":for(let t of e.value)yield*t;break}}function Zd(e,t,r){let n=0,i=0;for(let s of wo(e,t)){let a=0;for(let f of au(s))a++;i=Math.max(i,a),n+=a}let o=[[]];for(let s of wo(e,t))switch(s.type){case"parameter":{for(let a of o)a.push(s.value);break}case"stringChunk":break;case"parameterTuple":{let a=s.value.length,f=[];if(r&&o.length===1&&a===i&&n>r&&n-a<r){let w=r-(n-a);f=Xd(s.value,w)}else f=[s.value];o=o.flatMap(w=>f.map(A=>[...w,A]));break}case"parameterTupleList":{let a=s.value.reduce((C,I)=>C+I.length,0),f=[],w=[],A=0;for(let C of s.value)r&&o.length===1&&a===i&&w.length>0&&n-a+A+C.length>r&&(f.push(w),w=[],A=0),w.push(C),A+=C.length;w.length>0&&f.push(w),o=o.flatMap(C=>f.map(I=>[...C,I]));break}}return o}function Xd(e,t){let r=[];for(let n=0;n<e.length;n+=t)r.push(e.slice(n,n+t));return r}u();c();p();m();d();l();function lu(e){let t=e.columnTypes.map(r=>{switch(r){case O.Bytes:return n=>Array.isArray(n)?new Uint8Array(n):n;default:return n=>n}});return e.rows.map(r=>r.map((n,i)=>t[i](n)).reduce((n,i,o)=>{let s=e.columnNames[o].split("."),a=n;for(let f=0;f<s.length;f++){let w=s[f];f===s.length-1?a[w]=i:(a[w]===void 0&&(a[w]={}),a=a[w])}return n},{}))}function uu(e){return{columns:e.columnNames,types:e.columnTypes.map(t=>ef(t)),rows:e.rows.map(t=>t.map((r,n)=>Nt(r,e.columnTypes[n])))}}function Nt(e,t){if(e===null)return null;switch(t){case O.Int32:switch(typeof e){case"number":return Math.trunc(e);case"string":return Math.trunc(Number(e));default:throw new Error(`Cannot serialize value of type ${typeof e} as Int32`)}case O.Int32Array:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as Int32Array`);return e.map(r=>Nt(r,O.Int32));case O.Int64:switch(typeof e){case"number":return BigInt(Math.trunc(e));case"string":return e;default:throw new Error(`Cannot serialize value of type ${typeof e} as Int64`)}case O.Int64Array:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as Int64Array`);return e.map(r=>Nt(r,O.Int64));case O.Json:switch(typeof e){case"string":return JSON.parse(e);default:throw new Error(`Cannot serialize value of type ${typeof e} as Json`)}case O.JsonArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as JsonArray`);return e.map(r=>Nt(r,O.Json));case O.Bytes:if(Array.isArray(e))return new Uint8Array(e);throw new Error(`Cannot serialize value of type ${typeof e} as Bytes`);case O.BytesArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as BytesArray`);return e.map(r=>Nt(r,O.Bytes));case O.Boolean:switch(typeof e){case"boolean":return e;case"string":return e==="true"||e==="1";case"number":return e===1;default:throw new Error(`Cannot serialize value of type ${typeof e} as Boolean`)}case O.BooleanArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as BooleanArray`);return e.map(r=>Nt(r,O.Boolean));default:return e}}function ef(e){switch(e){case O.Int32:return"int";case O.Int64:return"bigint";case O.Float:return"float";case O.Double:return"double";case O.Text:return"string";case O.Enum:return"enum";case O.Bytes:return"bytes";case O.Boolean:return"bool";case O.Character:return"char";case O.Numeric:return"decimal";case O.Json:return"json";case O.Uuid:return"uuid";case O.DateTime:return"datetime";case O.Date:return"date";case O.Time:return"time";case O.Int32Array:return"int-array";case O.Int64Array:return"bigint-array";case O.FloatArray:return"float-array";case O.DoubleArray:return"double-array";case O.TextArray:return"string-array";case O.EnumArray:return"string-array";case O.BytesArray:return"bytes-array";case O.BooleanArray:return"bool-array";case O.CharacterArray:return"char-array";case O.NumericArray:return"decimal-array";case O.JsonArray:return"json-array";case O.UuidArray:return"uuid-array";case O.DateTimeArray:return"datetime-array";case O.DateArray:return"date-array";case O.TimeArray:return"time-array";case O.UnknownNumber:return"unknown";case O.Set:return"string";default:$(e,`Unexpected column type: ${e}`)}}u();c();p();m();d();l();function cu(e,t,r){if(!t.every(n=>Eo(e,n))){let n=tf(e,r),i=rf(r);throw new le(n,i,r.context)}}function Eo(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:$(t,`Unknown rule type: ${t.type}`)}}function tf(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:$(t,`Unknown error identifier: ${t}`)}}function rf(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:$(e,`Unknown error identifier: ${e}`)}}var Ar=class e{#e;#t;#r;#n=new qn;#o;#i;#u;#s;#l;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s,provider:a,connectionInfo:f}){this.#e=t,this.#t=r,this.#r=n,this.#o=i,this.#i=o,this.#u=s??o,this.#s=a,this.#l=f}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:lu,rawSerializer:uu,provider:t.provider,connectionInfo:t.connectionInfo})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#t,this.#n.snapshot(r.provider)).catch(i=>kt(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"value":return{value:Te(t.args,n,i)};case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!pu(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(xo(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>De(s)+De(a)):0}}case"execute":{let o=bo(t.args,n,i,this.#a()),s=0;for(let a of o)s+=await this.#c(a,r,()=>r.executeRaw(a).catch(f=>t.args.type==="rawSql"?ji(f):kt(f)));return{value:s}}case"query":{let o=bo(t.args,n,i,this.#a()),s;for(let a of o){let f=await this.#c(a,r,()=>r.queryRaw(a).catch(w=>t.args.type==="rawSql"?ji(w):kt(w)));s===void 0?s=f:(s.rows.push(...f.rows),s.lastInsertId=f.lastInsertId)}return{value:t.args.type==="rawSql"?this.#u(s):this.#i(s),lastInsertId:s?.lastInsertId}}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(pu(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:mu(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async f=>({joinExpr:f,childRecords:(await this.interpretNode(f.child,r,n,i)).value})));return{value:nf(o,a),lastInsertId:s}}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),a=o.getTransaction(s,"query");try{let f=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),f}catch(f){throw await o.rollbackTransaction(s.id),f}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:Xa(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return cu(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return Eo(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(xo(s).map(f=>JSON.stringify(f)));return{value:xo(o).filter(f=>!a.has(JSON.stringify(f)))}}case"process":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:Bn(o,t.args.operations),lastInsertId:s}}case"initializeRecord":{let{lastInsertId:o}=await this.interpretNode(t.args.expr,r,n,i),s={};for(let[a,f]of Object.entries(t.args.fields))s[a]=of(f,o,n,i);return{value:s,lastInsertId:o}}case"mapRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:Po(o);for(let[f,w]of Object.entries(t.args.fields))a[f]=sf(w,a[f],n,i);return{value:a,lastInsertId:s}}default:$(t,`Unexpected node type: ${t.type}`)}}#a(){return this.#l?.maxBindValues!==void 0?this.#l.maxBindValues:this.#p()}#p(){if(this.#s!==void 0)switch(this.#s){case"cockroachdb":case"postgres":case"postgresql":case"prisma+postgres":return 32766;case"mysql":return 65535;case"sqlite":return 999;case"sqlserver":return 2098;case"mongodb":return;default:$(this.#s,`Unexpected provider: ${this.#s}`)}}#c(t,r,n){return _n({query:t,execute:n,provider:this.#s??r.provider,tracingHelper:this.#o,onQuery:this.#r})}};function pu(e){return Array.isArray(e)?e.length===0:e==null}function xo(e){return Array.isArray(e)?e:[e]}function De(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function Po(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function mu(e,t){return Array.isArray(e)?e.map(r=>mu(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function nf(e,t){for(let{joinExpr:r,childRecords:n}of t){let i=r.on.map(([a])=>a),o=r.on.map(([,a])=>a),s={};for(let a of Array.isArray(e)?e:[e]){let f=Po(a),w=vr(f,i);s[w]||(s[w]=[]),s[w].push(f),r.isRelationUnique?f[r.parentField]=null:f[r.parentField]=[]}for(let a of Array.isArray(n)?n:[n]){if(a===null)continue;let f=vr(Po(a),o);for(let w of s[f]??[])r.isRelationUnique?w[r.parentField]=a:w[r.parentField].push(a)}}return e}function of(e,t,r,n){switch(e.type){case"value":return Te(e.value,r,n);case"lastInsertId":return t;default:$(e,`Unexpected field initializer type: ${e.type}`)}}function sf(e,t,r,n){switch(e.type){case"set":return Te(e.value,r,n);case"add":return De(t)+De(Te(e.value,r,n));case"subtract":return De(t)-De(Te(e.value,r,n));case"multiply":return De(t)*De(Te(e.value,r,n));case"divide":{let i=De(t),o=De(Te(e.value,r,n));return o===0?null:i/o}default:$(e,`Unexpected field operation type: ${e.type}`)}}u();c();p();m();d();l();u();c();p();m();d();l();async function af(){return globalThis.crypto??await Promise.resolve().then(()=>(Ze(),gi))}async function du(){return(await af()).randomUUID()}u();c();p();m();d();l();var be=class extends le{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Cr=class extends be{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},jn=class extends be{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Qn=class extends be{constructor(t){super(`Transaction is being closed: A ${t} cannot be executed on a closing transaction.`)}},Hn=class extends be{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},Gn=class extends be{constructor(){super("Unable to start a transaction in the given time.")}},Jn=class extends be{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction.`,{operation:t,timeout:r,timeTaken:n})}},Lt=class extends be{constructor(t){super(`Internal Consistency Error: ${t}`)}},Wn=class extends be{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var lf=100,Rr=K("prisma:client:transactionManager"),uf=()=>({sql:"COMMIT",args:[],argTypes:[]}),cf=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),pf=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),mf=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),Ir=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#e;#t;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i,provider:o}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#e=i,this.#t=o}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await du(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n);let i=!1,o=setTimeout(()=>i=!0,r.maxWait);switch(n.transaction=await this.driverAdapter.startTransaction(r.isolationLevel).catch(kt),clearTimeout(o),n.status){case"waiting":if(i)throw await this.closeTransaction(n,"timed_out"),new Gn;return n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"closing":case"timed_out":case"running":case"committed":case"rolled_back":throw new Lt(`Transaction in invalid state ${n.status} although it just finished startup.`);default:$(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new Cr;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Rr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"closing":case"waiting":case"running":throw new Lt("Active transaction found in closed transactions list.");case"committed":throw new jn(r);case"rolled_back":throw new Hn(r);case"timed_out":throw new Jn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Rr("Transaction not found.",t),new Cr}if(n.status==="closing")throw new Qn(r);if(["committed","rolled_back","timed_out"].includes(n.status))throw new Lt("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{Rr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):Rr("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){Rr("Closing transaction.",{transactionId:t.id,status:r}),t.status="closing";try{if(t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#n(pf(),t.transaction,()=>t.transaction.commit());else{let n=uf();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.commit()}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#n(mf(),t.transaction,()=>t.transaction.rollback());else{let n=cf();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.rollback()}}finally{t.status=r,clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>lf&&this.closedTransactions.shift()}}validateOptions(t){if(!t.timeout)throw new be("timeout is required");if(!t.maxWait)throw new be("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new Wn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#n(t,r,n){return _n({query:t,execute:n,provider:this.#t??r.provider,tracingHelper:this.tracingHelper,onQuery:this.#e})}};var Kn="6.14.0";u();c();p();m();d();l();var zn=class e{#e;#t;#r;#n;constructor(t,r,n){this.#e=t,this.#t=r,this.#r=n,this.#n=r.getConnectionInfo?.()}static async connect(t){let r,n;try{r=await t.driverAdapterFactory.connect(),n=new Ir({driverAdapter:r,transactionOptions:t.transactionOptions,tracingHelper:t.tracingHelper,onQuery:t.onQuery,provider:t.provider})}catch(i){throw await r?.dispose(),i}return new e(t,r,n)}getConnectionInfo(){let t=this.#n??{supportsRelationJoins:!1};return Promise.resolve({provider:this.#t.provider,connectionInfo:t})}async execute({plan:t,placeholderValues:r,transaction:n,batchIndex:i}){let o=n?this.#r.getTransaction(n,i!==void 0?"batch query":"query"):this.#t;return await Ar.forSql({transactionManager:n?{enabled:!1}:{enabled:!0,manager:this.#r},placeholderValues:r,onQuery:this.#e.onQuery,tracingHelper:this.#e.tracingHelper,provider:this.#e.provider,connectionInfo:this.#n}).run(t,o)}async startTransaction(t){return{...await this.#r.startTransaction(t),payload:void 0}}async commitTransaction(t){await this.#r.commitTransaction(t.id)}async rollbackTransaction(t){await this.#r.rollbackTransaction(t.id)}async disconnect(){try{await this.#r.cancelAllTransactions()}finally{await this.#t.dispose()}}};u();c();p();m();d();l();u();c();p();m();d();l();var Yn=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function fu(e,t,r){let n=r||{},i=n.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!Yn.test(e))throw new TypeError("argument name is invalid");let o=i(t);if(o&&!Yn.test(o))throw new TypeError("argument val is invalid");let s=e+"="+o;if(n.maxAge!==void 0&&n.maxAge!==null){let a=n.maxAge-0;if(Number.isNaN(a)||!Number.isFinite(a))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(a)}if(n.domain){if(!Yn.test(n.domain))throw new TypeError("option domain is invalid");s+="; Domain="+n.domain}if(n.path){if(!Yn.test(n.path))throw new TypeError("option path is invalid");s+="; Path="+n.path}if(n.expires){if(!ff(n.expires)||Number.isNaN(n.expires.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():n.priority){case"low":{s+="; Priority=Low";break}case"medium":{s+="; Priority=Medium";break}case"high":{s+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:{s+="; SameSite=Strict";break}case"lax":{s+="; SameSite=Lax";break}case"strict":{s+="; SameSite=Strict";break}case"none":{s+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return n.partitioned&&(s+="; Partitioned"),s}function ff(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}function gu(e,t){let r=(e||"").split(";").filter(f=>typeof f=="string"&&!!f.trim()),n=r.shift()||"",i=gf(n),o=i.name,s=i.value;try{s=t?.decode===!1?s:(t?.decode||decodeURIComponent)(s)}catch{}let a={name:o,value:s};for(let f of r){let w=f.split("="),A=(w.shift()||"").trimStart().toLowerCase(),C=w.join("=");switch(A){case"expires":{a.expires=new Date(C);break}case"max-age":{a.maxAge=Number.parseInt(C,10);break}case"secure":{a.secure=!0;break}case"httponly":{a.httpOnly=!0;break}case"samesite":{a.sameSite=C;break}default:a[A]=C}}return a}function gf(e){let t="",r="",n=e.split("=");return n.length>1?(t=n.shift(),r=n.join("=")):r=e,{name:t,value:r}}u();c();p();m();d();l();u();c();p();m();d();l();function Ut({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw gr().id==="workerd"?new F(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`,n):new F(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new F("error: Missing URL environment variable, value, or override.",n);return i}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var Zn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var fe=class extends Zn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};u();c();p();m();d();l();function N(e,t){return{...e,isRetryable:t}}var st=class extends fe{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,N(r,!1))}};D(st,"InvalidDatasourceError");function Xn(e){let t={clientVersion:e.clientVersion},r=Object.keys(e.inlineDatasources)[0],n=Ut({inlineDatasources:e.inlineDatasources,overrideDatasources:e.overrideDatasources,clientVersion:e.clientVersion,env:{...e.env,...typeof g<"u"?g.env:{}}}),i;try{i=new URL(n)}catch{throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==sn)throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new st(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);let f=hi(i)?"http:":"https:",w=new URL(i.href.replace(o,f));return{apiKey:a,url:w}}u();c();p();m();d();l();var yu=Ae(Ps()),Ft=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,transactionId:r}={}){let n={Accept:"application/json",Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json","Prisma-Engine-Hash":this.engineHash,"Prisma-Engine-Version":yu.enginesVersion};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-Transaction-Id"]=r);let i=this.#e();return i.length>0&&(n["X-Capture-Telemetry"]=i.join(", ")),n}#e(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}};u();c();p();m();d();l();function yf(e){return e[0]*1e3+e[1]/1e6}function Vt(e){return new Date(yf(e))}var hu=K("prisma:client:clientEngine:remoteExecutor"),ei=class{#e;#t;#r;#n;#o;constructor(t){this.#e=t.clientVersion,this.#n=t.logEmitter,this.#o=t.tracingHelper;let{url:r,apiKey:n}=Xn({clientVersion:t.clientVersion,env:t.env,inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources});this.#r=new To(r),this.#t=new Ft({apiKey:n,engineHash:t.clientVersion,logLevel:t.logLevel,logQueries:t.logQueries,tracingHelper:t.tracingHelper})}async getConnectionInfo(){return await this.#i({path:"/connection-info",method:"GET"})}async execute({plan:t,placeholderValues:r,batchIndex:n,model:i,operation:o,transaction:s,customFetch:a}){return(await this.#i({path:s?`/transaction/${s.id}/query`:"/query",method:"POST",body:{model:i,operation:o,plan:t,params:r},batchRequestIdx:n,fetch:a})).data}async startTransaction(t){return{...await this.#i({path:"/transaction/start",method:"POST",body:t}),payload:void 0}}async commitTransaction(t){await this.#i({path:`/transaction/${t.id}/commit`,method:"POST"})}async rollbackTransaction(t){await this.#i({path:`/transaction/${t.id}/rollback`,method:"POST"})}disconnect(){return Promise.resolve()}async#i({path:t,method:r,body:n,fetch:i=globalThis.fetch,batchRequestIdx:o}){let s=await this.#r.request({method:r,path:t,headers:this.#t.build(),body:n,fetch:i});s.ok||await this.#u(s,o);let a=await s.json();return typeof a.extensions=="object"&&a.extensions!==null&&this.#s(a.extensions),a}async#u(t,r){let n=t.headers.get("Prisma-Error-Code"),i=await t.text(),o,s=i;try{o=JSON.parse(i)}catch{o={}}typeof o.code=="string"&&(n=o.code),typeof o.error=="string"?s=o.error:typeof o.message=="string"?s=o.message:typeof o.InvalidRequestError=="object"&&o.InvalidRequestError!==null&&typeof o.InvalidRequestError.reason=="string"&&(s=o.InvalidRequestError.reason),s=s||`HTTP ${t.status}: ${t.statusText}`;let a=typeof o.meta=="object"&&o.meta!==null?o.meta:o;throw new ee(s,{clientVersion:this.#e,code:n??"P6000",batchRequestIdx:r,meta:a})}#s(t){if(t.logs)for(let r of t.logs)this.#l(r);t.traces&&this.#o.dispatchEngineSpans(t.traces)}#l(t){switch(t.level){case"debug":case"trace":hu(t);break;case"error":case"warn":case"info":{this.#n.emit(t.level,{timestamp:Vt(t.timestamp),message:t.attributes.message??"",target:t.target});break}case"query":{this.#n.emit("query",{query:t.attributes.query??"",timestamp:Vt(t.timestamp),duration:t.attributes.duration_ms??0,params:t.attributes.params??"",target:t.target});break}default:throw new Error(`Unexpected log level: ${t.level}`)}}},To=class{#e;#t;#r;constructor(t){this.#e=t,this.#t=new Map}async request({method:t,path:r,headers:n,body:i,fetch:o}){let s=new URL(r,this.#e),a=this.#n(s);a&&(n.Cookie=a),this.#r&&(n["Accelerate-Query-Engine-Jwt"]=this.#r);let f=await o(s,{method:t,body:i!==void 0?JSON.stringify(i):void 0,headers:n});return hu(t,s,f.status,f.statusText),this.#r=f.headers.get("Accelerate-Query-Engine-Jwt")??void 0,this.#o(s,f),f}#n(t){let r=[],n=new Date;for(let[i,o]of this.#t){if(o.expires&&o.expires<n){this.#t.delete(i);continue}let s=o.domain??t.hostname,a=o.path??"/";t.hostname.endsWith(s)&&t.pathname.startsWith(a)&&r.push(fu(o.name,o.value))}return r.length>0?r.join("; "):void 0}#o(t,r){let n=r.headers.getSetCookie?.()||[];if(n.length===0){let i=r.headers.get("Set-Cookie");i&&n.push(i)}for(let i of n){let o=gu(i),s=o.domain??t.hostname,a=o.path??"/",f=`${s}:${a}:${o.name}`;this.#t.set(f,{name:o.name,value:o.value,domain:s,path:a,expires:o.expires})}}};u();c();p();m();d();l();var vo,wu={async loadQueryCompiler(e){let{clientVersion:t,compilerWasm:r}=e;if(r===void 0)throw new F("WASM query compiler was unexpectedly `undefined`",t);return vo===void 0&&(vo=(async()=>{let n=await r.getRuntime(),i=await r.getQueryCompilerWasmModule();if(i==null)throw new F("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let o={"./query_compiler_bg.js":n},s=new WebAssembly.Instance(i,o),a=s.exports.__wbindgen_start;return n.__wbg_set_wasm(s.exports),a(),n.QueryCompiler})()),await vo}};var bu="P2038",Sr=K("prisma:client:clientEngine"),xu=globalThis;xu.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new de(e,Kn)}};var kr=class{name="ClientEngine";#e;#t={type:"disconnected"};#r;#n;config;datamodel;logEmitter;logQueries;logLevel;tracingHelper;#o;constructor(t,r,n){if(!t.previewFeatures?.includes("driverAdapters")&&!r)throw new F("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,bu);if(r)this.#n={remote:!0};else if(t.adapter)this.#n={remote:!1,driverAdapterFactory:t.adapter},Sr("Using driver adapter: %O",t.adapter);else throw new F("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,bu);this.#r=n??wu,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#o=i=>{this.logEmitter.emit("query",{...i,params:yr(i.params),target:"ClientEngine"})})}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async#i(){switch(this.#t.type){case"disconnected":{let t=this.tracingHelper.runInChildSpan("connect",async()=>{let r,n;try{r=await this.#u(),n=await this.#s(r)}catch(o){throw this.#t={type:"disconnected"},n?.free(),await r?.disconnect(),o}let i={executor:r,queryCompiler:n};return this.#t={type:"connected",engine:i},i});return this.#t={type:"connecting",promise:t},await t}case"connecting":return await this.#t.promise;case"connected":return this.#t.engine;case"disconnecting":return await this.#t.promise,await this.#i()}}async#u(){return this.#n.remote?new ei({clientVersion:this.config.clientVersion,env:this.config.env,inlineDatasources:this.config.inlineDatasources,logEmitter:this.logEmitter,logLevel:this.logLevel,logQueries:this.logQueries,overrideDatasources:this.config.overrideDatasources,tracingHelper:this.tracingHelper}):await zn.connect({driverAdapterFactory:this.#n.driverAdapterFactory,tracingHelper:this.tracingHelper,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#m(this.config.transactionOptions.isolationLevel)},onQuery:this.#o,provider:this.config.activeProvider})}async#s(t){let r=this.#e;r===void 0&&(r=await this.#r.loadQueryCompiler(this.config),this.#e=r);let{provider:n,connectionInfo:i}=await t.getConnectionInfo();try{return this.#c(()=>new r({datamodel:this.datamodel,provider:n,connectionInfo:i}),void 0,!1)}catch(o){throw this.#l(o)}}#l(t){if(t instanceof de)return t;try{let r=JSON.parse(t.message);return new F(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#a(t,r){if(t instanceof F)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new de(Eu(this,t.message,r),this.config.clientVersion);if(t instanceof le)return new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let n=JSON.parse(t);return new oe(`${n.message}
${n.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#p(t){return t instanceof de?t:typeof t.message=="string"&&typeof t.code=="string"?new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):typeof t.message=="string"?new oe(t.message,{clientVersion:this.config.clientVersion}):t}#c(t,r,n=!0){let i=xu.PRISMA_WASM_PANIC_REGISTRY.set_message,o;globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=s=>{o=s};try{return t()}finally{if(globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=i,o)throw this.#e=void 0,n&&this.stop().catch(s=>Sr("failed to disconnect:",s)),new de(Eu(this,o,r),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.#i()}async stop(){switch(this.#t.type){case"disconnected":return;case"connecting":return await this.#t.promise,await this.stop();case"connected":{let t=this.#t.engine,r=this.tracingHelper.runInChildSpan("disconnect",async()=>{try{await t.executor.disconnect(),t.queryCompiler.free()}finally{this.#t={type:"disconnected"}}});return this.#t={type:"disconnecting",promise:r},await r}case"disconnecting":return await this.#t.promise}}version(){return"unknown"}async transaction(t,r,n){let i,{executor:o}=await this.#i();try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#m(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s)}else Ne(t,"Invalid transaction action.")}catch(s){throw this.#a(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{interactiveTransaction:r,customDataProxyFetch:n}){Sr("sending request");let i=JSON.stringify(t),{executor:o,queryCompiler:s}=await this.#i().catch(f=>{throw this.#a(f,i)}),a;try{a=this.#c(()=>s.compile(i),i)}catch(f){throw this.#p(f)}try{Sr("query plan created",a);let f={},w=await o.execute({plan:a,model:t.modelName,operation:t.action,placeholderValues:f,transaction:r,batchIndex:void 0,customFetch:n?.(globalThis.fetch)});return Sr("query plan executed"),{data:{[t.action]:w}}}catch(f){throw this.#a(f,i)}}async requestBatch(t,{transaction:r,customDataProxyFetch:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(Rt(t,r)),{executor:s,queryCompiler:a}=await this.#i().catch(w=>{throw this.#a(w,o)}),f;try{f=a.compileBatch(o)}catch(w){throw this.#p(w)}try{let w;r?.kind==="itx"&&(w=r.options);let A={};switch(f.type){case"multi":{if(r?.kind!=="itx"){let R=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;w=await this.transaction("start",{},R)}let C=[],I=!1;for(let[R,L]of f.plans.entries())try{let k=await s.execute({plan:L,placeholderValues:A,model:t[R].modelName,operation:t[R].action,batchIndex:R,transaction:w,customFetch:n?.(globalThis.fetch)});C.push({data:{[t[R].action]:k}})}catch(k){C.push(k),I=!0;break}return w!==void 0&&r?.kind!=="itx"&&(I?await this.transaction("rollback",{},w):await this.transaction("commit",{},w)),C}case"compacted":{if(!t.every(R=>R.action===i))throw new Error("All queries in a batch must have the same action");let C=await s.execute({plan:f.plan,placeholderValues:A,model:t[0].modelName,operation:i,batchIndex:void 0,transaction:w,customFetch:n?.(globalThis.fetch)});return za(C,f).map(R=>({data:{[i]:R}}))}}}catch(w){throw this.#a(w,o)}}metrics(t){throw new Error("Method not implemented.")}#m(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new ee(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function Eu(e,t,r){return ja({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:r})}u();c();p();m();d();l();u();c();p();m();d();l();var $t=class extends fe{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",N(t,!0))}};D($t,"ForcedRetryError");u();c();p();m();d();l();var at=class extends fe{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,N(r,!1))}};D(at,"NotImplementedYetError");u();c();p();m();d();l();u();c();p();m();d();l();var Q=class extends fe{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var lt=class extends Q{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",N(t,!0))}};D(lt,"SchemaMissingError");u();c();p();m();d();l();u();c();p();m();d();l();var Ao="This request could not be understood by the server",Or=class extends Q{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||Ao,N(t,!1)),n&&(this.code=n)}};D(Or,"BadRequestError");u();c();p();m();d();l();var Dr=class extends Q{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",N(t,!0)),this.logs=r}};D(Dr,"HealthcheckTimeoutError");u();c();p();m();d();l();var _r=class extends Q{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,N(t,!0)),this.logs=n}};D(_r,"EngineStartupError");u();c();p();m();d();l();var Mr=class extends Q{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",N(t,!1))}};D(Mr,"EngineVersionNotSupportedError");u();c();p();m();d();l();var Co="Request timed out",Nr=class extends Q{name="GatewayTimeoutError";code="P5009";constructor(t,r=Co){super(r,N(t,!1))}};D(Nr,"GatewayTimeoutError");u();c();p();m();d();l();var hf="Interactive transaction error",Lr=class extends Q{name="InteractiveTransactionError";code="P5015";constructor(t,r=hf){super(r,N(t,!1))}};D(Lr,"InteractiveTransactionError");u();c();p();m();d();l();var wf="Request parameters are invalid",Ur=class extends Q{name="InvalidRequestError";code="P5011";constructor(t,r=wf){super(r,N(t,!1))}};D(Ur,"InvalidRequestError");u();c();p();m();d();l();var Ro="Requested resource does not exist",Fr=class extends Q{name="NotFoundError";code="P5003";constructor(t,r=Ro){super(r,N(t,!1))}};D(Fr,"NotFoundError");u();c();p();m();d();l();var Io="Unknown server error",qt=class extends Q{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||Io,N(t,!0)),this.logs=n}};D(qt,"ServerError");u();c();p();m();d();l();var So="Unauthorized, check your connection string",Vr=class extends Q{name="UnauthorizedError";code="P5007";constructor(t,r=So){super(r,N(t,!1))}};D(Vr,"UnauthorizedError");u();c();p();m();d();l();var ko="Usage exceeded, retry again later",$r=class extends Q{name="UsageExceededError";code="P5008";constructor(t,r=ko){super(r,N(t,!0))}};D($r,"UsageExceededError");async function bf(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function qr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await bf(e);if(n.type==="QueryEngineError")throw new ee(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new qt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new lt(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Mr(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new _r(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new F(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Dr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Lr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Ur(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Vr(r,Bt(So,n));if(e.status===404)return new Fr(r,Bt(Ro,n));if(e.status===429)throw new $r(r,Bt(ko,n));if(e.status===504)throw new Nr(r,Bt(Co,n));if(e.status>=500)throw new qt(r,Bt(Io,n));if(e.status>=400)throw new Or(r,Bt(Ao,n))}function Bt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}u();c();p();m();d();l();function Pu(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}u();c();p();m();d();l();var Ve="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Tu(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,f,w,A;for(let C=0;C<o;C=C+3)A=t[C]<<16|t[C+1]<<8|t[C+2],s=(A&16515072)>>18,a=(A&258048)>>12,f=(A&4032)>>6,w=A&63,r+=Ve[s]+Ve[a]+Ve[f]+Ve[w];return i==1?(A=t[o],s=(A&252)>>2,a=(A&3)<<4,r+=Ve[s]+Ve[a]+"=="):i==2&&(A=t[o]<<8|t[o+1],s=(A&64512)>>10,a=(A&1008)>>4,f=(A&15)<<2,r+=Ve[s]+Ve[a]+Ve[f]+"="),r}u();c();p();m();d();l();function vu(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new F("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}u();c();p();m();d();l();var Au={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};u();c();p();m();d();l();u();c();p();m();d();l();var Br=class extends fe{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,N(r,!0))}};D(Br,"RequestError");async function ut(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new Br(a,{clientVersion:n,cause:s})}}var xf=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,Cu=K("prisma:client:dataproxyEngine");async function Pf(e,t){let r=Au["@prisma/engines-version"],n=t.clientVersion??"unknown";if(g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&xf.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,f,w]=s.split("."),A=Tf(`<=${a}.${f}.${w}`),C=await ut(A,{clientVersion:n});if(!C.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${C.status} ${C.statusText}, response body: ${await C.text()||"<empty body>"}`);let I=await C.text();Cu("length of body fetched from unpkg.com",I.length);let R;try{R=JSON.parse(I)}catch(L){throw console.error("JSON.parse error: body fetched from unpkg.com: ",I),L}return R.version}throw new at("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function Ru(e,t){let r=await Pf(e,t);return Cu("version",r),r}function Tf(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Iu=3,jr=K("prisma:client:dataproxyEngine"),Qr=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){vu(t),this.config=t,this.env=t.env,this.inlineSchema=Tu(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.protocol=r.protocol,this.headerBuilder=new Ft({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel??"error",logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await Ru(this.host,this.config),jr("host",this.host),jr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":jr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:Vt(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:Vt(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}//${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await ut(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||jr("schema response status",r.status);let n=await qr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Rt(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(f=>(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f?this.convertProtocolErrorsToClientError(f.errors):f))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await ut(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,transactionId:i?.id}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||jr("graphql response status",a.status),await this.handleError(await qr(a,this.clientVersion));let f=await a.json();if(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f)throw this.convertProtocolErrorsToClientError(f.errors);return"batchResult"in f?f.batchResult:f}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let f=await ut(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await qr(f,this.clientVersion));let w=await f.json(),{extensions:A}=w;A&&this.propagateResponseExtensions(A);let C=w.id,I=w["data-proxy"].endpoint;return{id:C,payload:{endpoint:I}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await ut(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await qr(a,this.clientVersion));let f=await a.json(),{extensions:w}=f;w&&this.propagateResponseExtensions(w);return}}})}getURLAndAPIKey(){return Xn({clientVersion:this.clientVersion,env:this.env,inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources})}metrics(){throw new at("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof fe)||!i.isRetryable)throw i;if(r>=Iu)throw i instanceof $t?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${Iu} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await Pu(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof lt)throw await this.uploadSchema(),new $t({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?kn(t[0],this.config.clientVersion,this.config.activeProvider):new oe(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};u();c();p();m();d();l();function Su({url:e,adapter:t,copyEngine:r,targetBuildType:n}){let i=[],o=[],s=k=>{i.push({_tag:"warning",value:k})},a=k=>{let M=k.join(`
`);o.push({_tag:"error",value:M})},f=!!e?.startsWith("prisma://"),w=an(e),A=!!t,C=f||w;!A&&r&&C&&s(["recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);let I=C||!r;A&&(I||n==="edge")&&(n==="edge"?a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.","Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]):r?f&&a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]):a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));let R={accelerate:I,ppg:w,driverAdapters:A};function L(k){return k.length>0}return L(o)?{ok:!1,diagnostics:{warnings:i,errors:o},isUsing:R}:{ok:!0,diagnostics:{warnings:i},isUsing:R}}function ku({copyEngine:e=!0},t){let r;try{r=Ut({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...g.env},clientVersion:t.clientVersion})}catch{}let{ok:n,isUsing:i,diagnostics:o}=Su({url:r,adapter:t.adapter,copyEngine:e,targetBuildType:"wasm-compiler-edge"});for(let C of o.warnings)cn(...C.value);if(!n){let C=o.errors[0];throw new se(C.value,{clientVersion:t.clientVersion})}let s=ft(t.generator),a=s==="library",f=s==="binary",w=s==="client",A=(i.accelerate||i.ppg)&&!i.driverAdapters;if(w)return new kr(t,A);if(i.accelerate)return new Qr(t);i.driverAdapters,i.accelerate;{let C=[`PrismaClient failed to initialize because it wasn't configured to run in this environment (${gr().prettyName}).`,"In order to run Prisma Client in an edge runtime, you will need to configure one of the following options:","- Enable Driver Adapters: https://pris.ly/d/driver-adapters","- Enable Accelerate: https://pris.ly/d/accelerate"];throw new se(C.join(`
`),{clientVersion:t.clientVersion})}return"wasm-compiler-edge"}u();c();p();m();d();l();function ti({generator:e}){return e?.previewFeatures??[]}u();c();p();m();d();l();var Ou=e=>({command:e});u();c();p();m();d();l();u();c();p();m();d();l();var Du=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);u();c();p();m();d();l();l();function jt(e){try{return _u(e,"fast")}catch{return _u(e,"slow")}}function _u(e,t){return JSON.stringify(e.map(r=>Nu(r,t)))}function Nu(e,t){if(Array.isArray(e))return e.map(r=>Nu(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(yt(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(me.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(y.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(vf(e))return{prisma__type:"bytes",prisma__value:y.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:y.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Lu(e):e}function vf(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Lu(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(Mu);let t={};for(let r of Object.keys(e))t[r]=Mu(e[r]);return t}function Mu(e){return typeof e=="bigint"?e.toString():Lu(e)}var Af=/^(\s*alter\s)/i,Uu=K("prisma:client");function Oo(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&Af.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var Do=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(Cn(r))n=r.sql,i={values:jt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:jt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:jt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:jt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=Du(r),i={values:jt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Uu(`prisma.${e}(${n}, ${i.values})`):Uu(`prisma.${e}(${n})`),{query:n,parameters:i}},Fu={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new we(t,r)}},Vu={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};u();c();p();m();d();l();function _o(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=$u(r(s)):$u(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function $u(e){return typeof e.then=="function"?e:Promise.resolve(e)}u();c();p();m();d();l();var Cf=fi.split(".")[0],Rf={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Mo=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${Cf}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??Rf}};function qu(){return new Mo}u();c();p();m();d();l();function Bu(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}u();c();p();m();d();l();function ju(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}u();c();p();m();d();l();var Hu=Ae(bi());u();c();p();m();d();l();function ri(e){return typeof e.batchRequestIdx=="number"}u();c();p();m();d();l();function Qu(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(No(e.query.arguments)),t.push(No(e.query.selection)),t.join("")}function No(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${No(n)})`:r}).join(" ")})`}u();c();p();m();d();l();var If={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Lo(e){return If[e]}u();c();p();m();d();l();var ni=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,g.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};u();c();p();m();d();l();l();function ct(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new me(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>ct("bigint",r));case"bytes-array":return t.map(r=>ct("bytes",r));case"decimal-array":return t.map(r=>ct("decimal",r));case"datetime-array":return t.map(r=>ct("datetime",r));case"date-array":return t.map(r=>ct("date",r));case"time-array":return t.map(r=>ct("time",r));default:return t}}function Uo(e){let t=[],r=Sf(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=ct(e.types[s],i[s]);t.push(o)}return t}function Sf(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var kf=K("prisma:client:request_handler"),ii=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new ni({batchLoader:Da(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(C=>C.protocolQuery),f=this.client._tracingHelper.getTraceParent(s),w=n.some(C=>Lo(C.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:f,transaction:Of(o),containsWrite:w,customDataProxyFetch:i})).map((C,I)=>{if(C instanceof Error)return C;try{return this.mapQueryEngineResult(n[I],C)}catch(R){return R}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Gu(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Lo(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Qu(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return g.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(kf(t),Df(t,i))throw t;if(t instanceof ee&&_f(t)){let w=Ju(t.meta);Pn({args:o,errors:[w],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let f=t.message;if(n&&(f=dn({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:f})),f=this.sanitizeMessage(f),t.code){let w=s?{modelName:s,...t.meta}:t.meta;throw new ee(f,{code:t.code,clientVersion:this.client._clientVersion,meta:w,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new de(f,this.client._clientVersion);if(t instanceof oe)throw new oe(f,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof F)throw new F(f,this.client._clientVersion);if(t instanceof de)throw new de(f,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Hu.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(w=>w!=="select"&&w!=="include"),a=Li(o,s),f=i==="queryRaw"?Uo(a):et(a);return n?n(f):f}get[Symbol.toStringTag](){return"RequestHandler"}};function Of(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Gu(e)};Ne(e,"Unknown transaction kind")}}function Gu(e){return{id:e.id,payload:e.payload}}function Df(e,t){return ri(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function _f(e){return e.code==="P2009"||e.code==="P2012"}function Ju(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Ju)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}u();c();p();m();d();l();var Wu=Kn;u();c();p();m();d();l();var Xu=Ae(Ti());u();c();p();m();d();l();var V=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};D(V,"PrismaClientConstructorValidationError");var Ku=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],zu=["pretty","colorless","minimal"],Yu=["info","query","warn","error"],Mf={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new V(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Qt(r,t)||` Available datasources: ${t.join(", ")}`;throw new V(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new V(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new V(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new V(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&ft(t.generator)==="client")throw new V('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new V('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!ti(t).includes("driverAdapters"))throw new V('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(ft(t.generator)==="binary")throw new V('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new V(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new V(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!zu.includes(e)){let t=Qt(e,zu);throw new V(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new V(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Yu.includes(r)){let n=Qt(r,Yu);throw new V(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Qt(i,o);throw new V(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new V(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new V(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new V(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new V('"omit" option is expected to be an object.');if(e===null)throw new V('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=Lf(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let f=o.fields.find(w=>w.name===s);if(!f){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(f.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new V(Uf(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new V(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Qt(r,t);throw new V(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function ec(e,t){for(let[r,n]of Object.entries(e)){if(!Ku.includes(r)){let i=Qt(r,Ku);throw new V(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Mf[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new V('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Qt(e,t){if(t.length===0||typeof e!="string")return"";let r=Nf(e,t);return r?` Did you mean "${r}"?`:""}function Nf(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Xu.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Lf(e,t){return Zu(t.models,e)??Zu(t.types,e)}function Zu(e,t){let r=Object.keys(e).find(n=>qe(n)===t);if(r)return e[r]}function Uf(e,t){let r=vt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=xn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}u();c();p();m();d();l();function tc(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},f=w=>{o||(o=!0,r(w))};for(let w=0;w<e.length;w++)e[w].then(A=>{n[w]=A,a()},A=>{if(!ri(A)){f(A);return}A.batchRequestIdx===w?f(A):(i||(i=A),a())})})}var Je=K("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var Ff={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Vf=Symbol.for("prisma.client.transaction.id"),$f={id:0,nextId(){return++this.id}};function qf(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=_o();constructor(n){e=n?.__internal?.configOverride?.(e)??e,Ua(e),n&&ec(n,e);let i=new Rn().on("error",()=>{});this._extensions=At.empty(),this._previewFeatures=ti(e),this._clientVersion=e.clientVersion??Wu,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=qu();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&en.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&en.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let f=e.activeProvider==="postgresql"||e.activeProvider==="cockroachdb"?"postgres":e.activeProvider;if(s.provider!==f)throw new F(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new F("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=e.injectableEdgeEnv?.();try{let f=n??{},w=f.__internal??{},A=w.debug===!0;A&&K.enable("prisma:client");let C=en.resolve(e.dirname,e.relativePath);ds.existsSync(C)||(C=e.dirname),Je("dirname",e.dirname),Je("relativePath",e.relativePath),Je("cwd",C);let I=w.engine||{};if(f.errorFormat?this._errorFormat=f.errorFormat:g.env.NODE_ENV==="production"?this._errorFormat="minimal":g.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:C,dirname:e.dirname,enableDebugLogs:A,allowTriggerPanic:I.allowTriggerPanic,prismaPath:I.binaryPath??void 0,engineEndpoint:I.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:f.log&&ju(f.log),logQueries:f.log&&!!(typeof f.log=="string"?f.log==="query":f.log.find(R=>typeof R=="string"?R==="query":R.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:Fa(f,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:f.transactionOptions?.maxWait??2e3,timeout:f.transactionOptions?.timeout??5e3,isolationLevel:f.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Ut,getBatchRequestPayload:Rt,prismaGraphQLToJSError:kn,PrismaClientUnknownRequestError:oe,PrismaClientInitializationError:F,PrismaClientKnownRequestError:ee,debug:K("prisma:client:accelerateEngine"),engineVersion:nc.version,clientVersion:e.clientVersion}},Je("clientVersion",e.clientVersion),this._engine=ku(e,this._engineConfig),this._requestHandler=new ii(this,i),f.log)for(let R of f.log){let L=typeof R=="string"?R:R.emit==="stdout"?R.level:null;L&&this.$on(L,k=>{Zt.log(`${Zt.tags[L]??""}`,k.message||k.query)})}}catch(f){throw f.clientVersion=this._clientVersion,f}return this._appliedParent=dr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{ms()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:Do({clientMethod:i,activeProvider:a}),callsite:je(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=rc(n,i);return Oo(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new se("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(Oo(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new se(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Ou,callsite:je(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:Do({clientMethod:i,activeProvider:a}),callsite:je(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...rc(n,i));throw new se("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new se("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=$f.nextId(),s=Bu(n.length),a=n.map((f,w)=>{if(f?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let A=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,C={kind:"batch",id:o,index:w,isolationLevel:A,lock:s};return f.requestTransaction?.(C)??f});return tc(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),f;try{let w={kind:"itx",...a};f=await n(this._createItxClient(w)),await this._engine.transaction("commit",o,a)}catch(w){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),w}return f}_createItxClient(n){return Pe(dr(Pe(Pa(this),[ae("_appliedParent",()=>this._appliedParent._createItxClient(n)),ae("_createPrismaPromise",()=>_o(n)),ae(Vf,()=>n.id)])),[Ct(Ra)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??Ff,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=async f=>{let{runInTransaction:w,args:A,...C}=f,I={...n,...C};A&&(I.args=i.middlewareArgsToRequestArgs(A)),n.transaction!==void 0&&w===!1&&delete I.transaction;let R=await Oa(this,I);return I.model?Ca({result:R,modelName:I.model,args:I.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):R};return this._tracingHelper.runInChildSpan(s.operation,()=>a(o))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:f,argsMapper:w,transaction:A,unpacker:C,otelParentCtx:I,customDataProxyFetch:R}){try{n=w?w(n):n;let L={name:"serialize"},k=this._tracingHelper.runInChildSpan(L,()=>Oi({modelName:f,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return K.enabled("prisma:client")&&(Je("Prisma Client call:"),Je(`prisma.${i}(${ma(n)})`),Je("Generated request:"),Je(JSON.stringify(k,null,2)+`
`)),A?.kind==="batch"&&await A.lock,this._requestHandler.request({protocolQuery:k,modelName:f,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:A,unpacker:C,otelParentCtx:I,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:R})}catch(L){throw L.clientVersion=this._clientVersion,L}}$metrics=new cr(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=Ta}return t}function rc(e,t){return Bf(e)?[new we(e,t),Fu]:[e,Vu]}function Bf(e){return Array.isArray(e)&&Array.isArray(e.raw)}u();c();p();m();d();l();var jf=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Qf(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!jf.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}u();c();p();m();d();l();l();var export_warnEnvConflicts=void 0;export{mn as DMMF,K as Debug,me as Decimal,Yo as Extensions,cr as MetricsClient,F as PrismaClientInitializationError,ee as PrismaClientKnownRequestError,de as PrismaClientRustPanicError,oe as PrismaClientUnknownRequestError,se as PrismaClientValidationError,Xo as Public,we as Sql,jp as createParam,Xp as defineDmmfProperty,et as deserializeJsonResponse,Uo as deserializeRawResult,lp as dmmfToRuntimeDataModel,nm as empty,qf as getPrismaClient,gr as getRuntime,rm as join,Qf as makeStrictEnum,tm as makeTypedQueryFactory,Ci as objectEnumValues,la as raw,Oi as serializeJsonQuery,Si as skip,ua as sqltag,export_warnEnvConflicts as warnEnvConflicts,cn as warnOnce};
//# sourceMappingURL=wasm-compiler-edge.mjs.map
