{"name": "@prisma/engines-version", "version": "6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49", "main": "index.js", "types": "index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "prisma": {"enginesVersion": "717184b7b35ea05dfa71a3236b7af656013e1e49"}, "repository": {"type": "git", "url": "https://github.com/prisma/engines-wrapper.git", "directory": "packages/engines-version"}, "devDependencies": {"@types/node": "18.19.76", "typescript": "4.9.5"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc -d"}}