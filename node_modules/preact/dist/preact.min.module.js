var n,l,t,u,e,i,r,o,f,c,s,a,h,p,y={},v=[],d=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,w=Array.isArray;function m(n,l){for(var t in l)n[t]=l[t];return n}function _(n){n&&n.parentNode&&n.parentNode.removeChild(n)}function g(l,t,u){var e,i,r,o={};for(r in t)"key"==r?e=t[r]:"ref"==r?i=t[r]:o[r]=t[r];if(arguments.length>2&&(o.children=arguments.length>3?n.call(arguments,2):u),"function"==typeof l&&null!=l.defaultProps)for(r in l.defaultProps)void 0===o[r]&&(o[r]=l.defaultProps[r]);return b(l,o,e,i,null)}function b(n,u,e,i,r){var o={type:n,props:u,key:e,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==r?++t:r,__i:-1,__u:0};return null==r&&null!=l.vnode&&l.vnode(o),o}function k(n){return n.children}function x(n,l){this.props=n,this.context=l}function C(n,l){if(null==l)return n.__?C(n.__,n.__i+1):null;for(var t;l<n.__k.length;l++)if(null!=(t=n.__k[l])&&null!=t.__e)return t.__e;return"function"==typeof n.type?C(n):null}function S(n){var l,t;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(t=n.__k[l])&&null!=t.__e){n.__e=n.__c.base=t.__e;break}return S(n)}}function M(n){(!n.__d&&(n.__d=!0)&&e.push(n)&&!$.__r++||i!=l.debounceRendering)&&((i=l.debounceRendering)||r)($)}function $(){for(var n,t,u,i,r,f,c,s=1;e.length;)e.length>s&&e.sort(o),n=e.shift(),s=e.length,n.__d&&(u=void 0,r=(i=(t=n).__v).__e,f=[],c=[],t.__P&&((u=m({},i)).__v=i.__v+1,l.vnode&&l.vnode(u),T(t.__P,u,i,t.__n,t.__P.namespaceURI,32&i.__u?[r]:null,f,null==r?C(i):r,!!(32&i.__u),c),u.__v=i.__v,u.__.__k[u.__i]=u,O(f,u,c),u.__e!=r&&S(u)));$.__r=0}function A(n,l,t,u,e,i,r,o,f,c,s){var a,h,p,d,w,m,_,g=u&&u.__k||v,b=l.length;for(f=I(t,l,g,f,b),a=0;a<b;a++)null!=(p=t.__k[a])&&(h=-1==p.__i?y:g[p.__i]||y,p.__i=a,m=T(n,p,h,e,i,r,o,f,c,s),d=p.__e,p.ref&&h.ref!=p.ref&&(h.ref&&N(h.ref,null,p),s.push(p.ref,p.__c||d,p)),null==w&&null!=d&&(w=d),(_=!!(4&p.__u))||h.__k===p.__k?f=P(p,f,n,_):"function"==typeof p.type&&void 0!==m?f=m:d&&(f=d.nextSibling),p.__u&=-7);return t.__e=w,f}function I(n,l,t,u,e){var i,r,o,f,c,s=t.length,a=s,h=0;for(n.__k=new Array(e),i=0;i<e;i++)null!=(r=l[i])&&"boolean"!=typeof r&&"function"!=typeof r?(f=i+h,(r=n.__k[i]="string"==typeof r||"number"==typeof r||"bigint"==typeof r||r.constructor==String?b(null,r,null,null,null):w(r)?b(k,{children:r},null,null,null):null==r.constructor&&r.__b>0?b(r.type,r.props,r.key,r.ref?r.ref:null,r.__v):r).__=n,r.__b=n.__b+1,o=null,-1!=(c=r.__i=E(r,t,f,a))&&(a--,(o=t[c])&&(o.__u|=2)),null==o||null==o.__v?(-1==c&&(e>s?h--:e<s&&h++),"function"!=typeof r.type&&(r.__u|=4)):c!=f&&(c==f-1?h--:c==f+1?h++:(c>f?h--:h++,r.__u|=4))):n.__k[i]=null;if(a)for(i=0;i<s;i++)null!=(o=t[i])&&0==(2&o.__u)&&(o.__e==u&&(u=C(o)),R(o,o));return u}function P(n,l,t,u){var e,i;if("function"==typeof n.type){for(e=n.__k,i=0;e&&i<e.length;i++)e[i]&&(e[i].__=n,l=P(e[i],l,t,u));return l}n.__e!=l&&(u&&(l&&n.type&&!l.parentNode&&(l=C(n)),t.insertBefore(n.__e,l||null)),l=n.__e);do{l=l&&l.nextSibling}while(null!=l&&8==l.nodeType);return l}function E(n,l,t,u){var e,i,r,o=n.key,f=n.type,c=l[t],s=null!=c&&0==(2&c.__u);if(null===c&&null==n.key||s&&o==c.key&&f==c.type)return t;if(u>(s?1:0))for(e=t-1,i=t+1;e>=0||i<l.length;)if(null!=(c=l[r=e>=0?e--:i++])&&0==(2&c.__u)&&o==c.key&&f==c.type)return r;return-1}function F(n,l,t){"-"==l[0]?n.setProperty(l,null==t?"":t):n[l]=null==t?"":"number"!=typeof t||d.test(l)?t:t+"px"}function H(n,l,t,u,e){var i,r;n:if("style"==l)if("string"==typeof t)n.style.cssText=t;else{if("string"==typeof u&&(n.style.cssText=u=""),u)for(l in u)t&&l in t||F(n.style,l,"");if(t)for(l in t)u&&t[l]==u[l]||F(n.style,l,t[l])}else if("o"==l[0]&&"n"==l[1])i=l!=(l=l.replace(f,"$1")),r=l.toLowerCase(),l=r in n||"onFocusOut"==l||"onFocusIn"==l?r.slice(2):l.slice(2),n.l||(n.l={}),n.l[l+i]=t,t?u?t.t=u.t:(t.t=c,n.addEventListener(l,i?a:s,i)):n.removeEventListener(l,i?a:s,i);else{if("http://www.w3.org/2000/svg"==e)l=l.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=l&&"height"!=l&&"href"!=l&&"list"!=l&&"form"!=l&&"tabIndex"!=l&&"download"!=l&&"rowSpan"!=l&&"colSpan"!=l&&"role"!=l&&"popover"!=l&&l in n)try{n[l]=null==t?"":t;break n}catch(n){}"function"==typeof t||(null==t||!1===t&&"-"!=l[4]?n.removeAttribute(l):n.setAttribute(l,"popover"==l&&1==t?"":t))}}function L(n){return function(t){if(this.l){var u=this.l[t.type+n];if(null==t.u)t.u=c++;else if(t.u<u.t)return;return u(l.event?l.event(t):t)}}}function T(n,t,u,e,i,r,o,f,c,s){var a,h,p,y,v,d,g,b,C,S,M,$,I,P,E,F,H,L=t.type;if(null!=t.constructor)return null;128&u.__u&&(c=!!(32&u.__u),r=[f=t.__e=u.__e]),(a=l.__b)&&a(t);n:if("function"==typeof L)try{if(b=t.props,C="prototype"in L&&L.prototype.render,S=(a=L.contextType)&&e[a.__c],M=a?S?S.props.value:a.__:e,u.__c?g=(h=t.__c=u.__c).__=h.__E:(C?t.__c=h=new L(b,M):(t.__c=h=new x(b,M),h.constructor=L,h.render=q),S&&S.sub(h),h.props=b,h.state||(h.state={}),h.context=M,h.__n=e,p=h.__d=!0,h.__h=[],h._sb=[]),C&&null==h.__s&&(h.__s=h.state),C&&null!=L.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=m({},h.__s)),m(h.__s,L.getDerivedStateFromProps(b,h.__s))),y=h.props,v=h.state,h.__v=t,p)C&&null==L.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),C&&null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(C&&null==L.getDerivedStateFromProps&&b!==y&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(b,M),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(b,h.__s,M)||t.__v==u.__v){for(t.__v!=u.__v&&(h.props=b,h.state=h.__s,h.__d=!1),t.__e=u.__e,t.__k=u.__k,t.__k.some(function(n){n&&(n.__=t)}),$=0;$<h._sb.length;$++)h.__h.push(h._sb[$]);h._sb=[],h.__h.length&&o.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(b,h.__s,M),C&&null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(y,v,d)})}if(h.context=M,h.props=b,h.__P=n,h.__e=!1,I=l.__r,P=0,C){for(h.state=h.__s,h.__d=!1,I&&I(t),a=h.render(h.props,h.state,h.context),E=0;E<h._sb.length;E++)h.__h.push(h._sb[E]);h._sb=[]}else do{h.__d=!1,I&&I(t),a=h.render(h.props,h.state,h.context),h.state=h.__s}while(h.__d&&++P<25);h.state=h.__s,null!=h.getChildContext&&(e=m(m({},e),h.getChildContext())),C&&!p&&null!=h.getSnapshotBeforeUpdate&&(d=h.getSnapshotBeforeUpdate(y,v)),F=a,null!=a&&a.type===k&&null==a.key&&(F=V(a.props.children)),f=A(n,w(F)?F:[F],t,u,e,i,r,o,f,c,s),h.base=t.__e,t.__u&=-161,h.__h.length&&o.push(h),g&&(h.__E=h.__=null)}catch(n){if(t.__v=null,c||null!=r)if(n.then){for(t.__u|=c?160:128;f&&8==f.nodeType&&f.nextSibling;)f=f.nextSibling;r[r.indexOf(f)]=null,t.__e=f}else{for(H=r.length;H--;)_(r[H]);j(t)}else t.__e=u.__e,t.__k=u.__k,n.then||j(t);l.__e(n,t,u)}else null==r&&t.__v==u.__v?(t.__k=u.__k,t.__e=u.__e):f=t.__e=z(u.__e,t,u,e,i,r,o,c,s);return(a=l.diffed)&&a(t),128&t.__u?void 0:f}function j(n){n&&n.__c&&(n.__c.__e=!0),n&&n.__k&&n.__k.forEach(j)}function O(n,t,u){for(var e=0;e<u.length;e++)N(u[e],u[++e],u[++e]);l.__c&&l.__c(t,n),n.some(function(t){try{n=t.__h,t.__h=[],n.some(function(n){n.call(t)})}catch(n){l.__e(n,t.__v)}})}function V(n){return"object"!=typeof n||null==n||n.__b&&n.__b>0?n:w(n)?n.map(V):m({},n)}function z(t,u,e,i,r,o,f,c,s){var a,h,p,v,d,m,g,b=e.props,k=u.props,x=u.type;if("svg"==x?r="http://www.w3.org/2000/svg":"math"==x?r="http://www.w3.org/1998/Math/MathML":r||(r="http://www.w3.org/1999/xhtml"),null!=o)for(a=0;a<o.length;a++)if((d=o[a])&&"setAttribute"in d==!!x&&(x?d.localName==x:3==d.nodeType)){t=d,o[a]=null;break}if(null==t){if(null==x)return document.createTextNode(k);t=document.createElementNS(r,x,k.is&&k),c&&(l.__m&&l.__m(u,o),c=!1),o=null}if(null==x)b===k||c&&t.data==k||(t.data=k);else{if(o=o&&n.call(t.childNodes),b=e.props||y,!c&&null!=o)for(b={},a=0;a<t.attributes.length;a++)b[(d=t.attributes[a]).name]=d.value;for(a in b)if(d=b[a],"children"==a);else if("dangerouslySetInnerHTML"==a)p=d;else if(!(a in k)){if("value"==a&&"defaultValue"in k||"checked"==a&&"defaultChecked"in k)continue;H(t,a,null,d,r)}for(a in k)d=k[a],"children"==a?v=d:"dangerouslySetInnerHTML"==a?h=d:"value"==a?m=d:"checked"==a?g=d:c&&"function"!=typeof d||b[a]===d||H(t,a,d,b[a],r);if(h)c||p&&(h.__html==p.__html||h.__html==t.innerHTML)||(t.innerHTML=h.__html),u.__k=[];else if(p&&(t.innerHTML=""),A("template"==u.type?t.content:t,w(v)?v:[v],u,e,i,"foreignObject"==x?"http://www.w3.org/1999/xhtml":r,o,f,o?o[0]:e.__k&&C(e,0),c,s),null!=o)for(a=o.length;a--;)_(o[a]);c||(a="value","progress"==x&&null==m?t.removeAttribute("value"):null!=m&&(m!==t[a]||"progress"==x&&!m||"option"==x&&m!=b[a])&&H(t,a,m,b[a],r),a="checked",null!=g&&g!=t[a]&&H(t,a,g,b[a],r))}return t}function N(n,t,u){try{if("function"==typeof n){var e="function"==typeof n.__u;e&&n.__u(),e&&null==t||(n.__u=n(t))}else n.current=t}catch(n){l.__e(n,u)}}function R(n,t,u){var e,i;if(l.unmount&&l.unmount(n),(e=n.ref)&&(e.current&&e.current!=n.__e||N(e,null,t)),null!=(e=n.__c)){if(e.componentWillUnmount)try{e.componentWillUnmount()}catch(n){l.__e(n,t)}e.base=e.__P=null}if(e=n.__k)for(i=0;i<e.length;i++)e[i]&&R(e[i],t,u||"function"!=typeof n.type);u||_(n.__e),n.__c=n.__=n.__e=void 0}function q(n,l,t){return this.constructor(n,t)}function B(t,u,e){var i,r,o,f;u==document&&(u=document.documentElement),l.__&&l.__(t,u),r=(i="function"==typeof e)?null:e&&e.__k||u.__k,o=[],f=[],T(u,t=(!i&&e||u).__k=g(k,null,[t]),r||y,y,u.namespaceURI,!i&&e?[e]:r?null:u.firstChild?n.call(u.childNodes):null,o,!i&&e?e:r?r.__e:u.firstChild,i,f),O(o,t,f)}n=v.slice,l={__e:function(n,l,t,u){for(var e,i,r;l=l.__;)if((e=l.__c)&&!e.__)try{if((i=e.constructor)&&null!=i.getDerivedStateFromError&&(e.setState(i.getDerivedStateFromError(n)),r=e.__d),null!=e.componentDidCatch&&(e.componentDidCatch(n,u||{}),r=e.__d),r)return e.__E=e}catch(l){n=l}throw n}},t=0,u=function(n){return null!=n&&null==n.constructor},x.prototype.setState=function(n,l){var t;t=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=m({},this.state),"function"==typeof n&&(n=n(m({},t),this.props)),n&&m(t,n),null!=n&&this.__v&&(l&&this._sb.push(l),M(this))},x.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),M(this))},x.prototype.render=k,e=[],r="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,o=function(n,l){return n.__v.__b-l.__v.__b},$.__r=0,f=/(PointerCapture)$|Capture$/i,c=0,s=L(!1),a=L(!0),h=0,p={__proto__:null,render:B,hydrate:function n(l,t){B(l,t,n)},createElement:g,h:g,Fragment:k,createRef:function(){return{current:null}},isValidElement:u,Component:x,cloneElement:function(l,t,u){var e,i,r,o,f=m({},l.props);for(r in l.type&&l.type.defaultProps&&(o=l.type.defaultProps),t)"key"==r?e=t[r]:"ref"==r?i=t[r]:f[r]=void 0===t[r]&&null!=o?o[r]:t[r];return arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):u),b(l.type,f,e||l.key,i||l.ref,null)},createContext:function(n){function l(n){var t,u;return this.getChildContext||(t=new Set,(u={})[l.__c]=this,this.getChildContext=function(){return u},this.componentWillUnmount=function(){t=null},this.shouldComponentUpdate=function(n){this.props.value!=n.value&&t.forEach(function(n){n.__e=!0,M(n)})},this.sub=function(n){t.add(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){t&&t.delete(n),l&&l.call(n)}}),n.children}return l.__c="__cC"+h++,l.__=n,l.Provider=l.__l=(l.Consumer=function(n,l){return n.children(l)}).contextType=l,l},toChildArray:function n(l,t){return t=t||[],null==l||"boolean"==typeof l||(w(l)?l.some(function(l){n(l,t)}):t.push(l)),t},options:l},typeof module<"u"?module.exports=p:self.preact=p;
//# sourceMappingURL=preact.min.module.js.map
