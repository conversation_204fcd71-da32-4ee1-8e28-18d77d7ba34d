{"version": 3, "file": "devtools.module.js", "sources": ["../src/devtools.js", "../src/index.js"], "sourcesContent": ["import { Component, Fragment, options } from 'preact';\n\nexport function initDevTools() {\n\tconst globalVar =\n\t\ttypeof globalThis !== 'undefined'\n\t\t\t? globalThis\n\t\t\t: typeof window !== 'undefined'\n\t\t\t\t? window\n\t\t\t\t: undefined;\n\n\tif (\n\t\tglobalVar !== null &&\n\t\tglobalVar !== undefined &&\n\t\tglobalVar.__PREACT_DEVTOOLS__\n\t) {\n\t\tglobalVar.__PREACT_DEVTOOLS__.attachPreact('10.27.1', options, {\n\t\t\tFragment,\n\t\t\tComponent\n\t\t});\n\t}\n}\n", "import { options } from 'preact';\nimport { initDevTools } from './devtools';\n\ninitDevTools();\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, name: string) => T}\n */\nexport function addHookName(value, name) {\n\tif (options._addHookName) {\n\t\toptions._addHookName(name);\n\t}\n\treturn value;\n}\n"], "names": ["globalVar", "addHookName", "value", "name", "options", "__a", "globalThis", "window", "undefined", "__PREACT_DEVTOOLS__", "attachPreact", "Fragment", "Component"], "mappings": "8DAEgB,IACTA,ECMS,SAAAC,EAAYC,EAAOC,GAIlC,OAHIC,EAAOC,KACVD,EAAOC,IAAcF,GAEfD,CACR,CDHEF,OARKA,EACiB,oBAAfM,WACJA,WACkB,oBAAXC,OACNA,YACAC,IAKJR,EAAUS,qBAEVT,EAAUS,oBAAoBC,aAAa,UAAWN,EAAS,CAC9DO,SAAAA,EACAC,UAAAA"}