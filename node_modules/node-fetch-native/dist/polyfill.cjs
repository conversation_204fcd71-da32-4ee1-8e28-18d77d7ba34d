"use strict";var n=Object.defineProperty;var i=(r,o)=>n(r,"name",{value:o,configurable:!0});const node=require("./node.cjs");require("node:http"),require("node:https"),require("node:zlib"),require("node:stream"),require("node:buffer"),require("node:util"),require("./shared/node-fetch-native.DhEqb06g.cjs"),require("node:url"),require("node:net"),require("node:fs"),require("node:path");var t=Object.defineProperty,a=i((r,o)=>t(r,"name",{value:o,configurable:!0}),"a");function e(r,o){if(!(r in globalThis))try{globalThis[r]=o}catch{}}i(e,"e"),a(e,"polyfill"),e("fetch",node.fetch),e("Blob",node.Blob),e("File",node.File),e("FormData",node.FormData),e("Headers",node.Headers),e("Request",node.Request),e("Response",node.Response),e("AbortController",node.AbortController);
